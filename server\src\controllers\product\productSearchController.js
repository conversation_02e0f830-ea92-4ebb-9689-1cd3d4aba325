const { searchProducts } = require('../../services/product/productSearchService');
const { success, error } = require('../../utils/response');
const { searchProductsSchema } = require('../../validators/productvalidator');

/**
 * @desc    智能搜索产品
 * @route   GET /api/v1/products/search
 * @access  Public
 */
const searchProductsController = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = searchProductsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { keyword, limit, category } = value;

    // 调用服务层进行智能产品搜索
    const result = await searchProducts(keyword, limit, category);

    if (!result.success) {
      return error(res, 400, result.message, result.data);
    }

    // 返回成功响应
    const responseData = {
      products: result.data.products,
      pagination: result.data.pagination,
      meta: {
        keyword: result.data.keyword,
        requestedLimit: limit,
        category: category || 'all',
        description: '智能搜索产品，支持模糊匹配和分词搜索'
      }
    };

    return success(res, 200, result.message, responseData);

  } catch (err) {
    console.error('智能产品搜索控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('搜索关键词')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('返回数量')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('产品类别')) {
      return error(res, 400, err.message);
    }

    return error(res, 500, '智能搜索服务暂时不可用，请稍后重试');
  }
};

module.exports = {
  searchProductsController
};