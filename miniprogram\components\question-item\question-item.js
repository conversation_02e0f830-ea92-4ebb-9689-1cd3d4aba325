const { question: questionApi, result: resultApi } = require('../../utils/api');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    question: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击问题时的处理
     */
    onQuestionTap: function() {
      const questionId = this.properties.question.id;
      this.triggerEvent('questiontap', { id: questionId });
    },

    /**
     * 预览图片
     */
    previewImage: function(e) {
      const urls = e.currentTarget.dataset.urls;
      const current = e.currentTarget.dataset.current;
      
      wx.previewImage({
        urls: urls,
        current: current
      });
      // 阻止事件冒泡，防止触发问题点击
      return false;
    },
    
    /**
     * 查看问题结果
     */
    viewResult: function(e) {
      const questionId = this.properties.question.id;
      
      // 无需手动阻止事件冒泡，catchtap已经处理了
      
      // 跳转到结果页面
      wx.navigateTo({
        url: `/pages/result/result?id=${questionId}`
      });
    }
  }
}) 