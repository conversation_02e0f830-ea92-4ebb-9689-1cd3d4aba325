# 社区模块 API 测试文档

本文档将指导您如何使用 Postman 测试"听听"微信小程序社区模块的 API。

## 目录

1. [准备工作](#准备工作)
2. [问题相关 API](#问题相关-api)
3. [回答相关 API](#回答相关-api)
4. [评论相关 API](#评论相关-api)

## 准备工作

### 安装 Postman

1. 前往 [Postman 官网](https://www.postman.com/downloads/) 下载并安装 Postman
2. 打开 Postman 并创建一个新的 Collection，命名为"听听社区 API"

### 设置环境变量

1. 点击右上角的"环境"按钮，创建一个新环境(如"听听开发环境")
2. 添加以下变量：
   - `BASE_URL`：设置为您的 API 基础 URL，例如 `http://localhost:5000/api/v1`
   - `TOKEN`：用于存储登录后的认证令牌(后续会获取)

### 获取认证令牌

在测试任何 API 之前，您需要先登录并获取认证令牌：

1. 创建一个新的 POST 请求：`{{BASE_URL}}/auth/login`
2. 在 Body 选项卡中选择 "raw" 和 "JSON" 格式，输入以下内容：
   ```json
   {
     "phone": "您的手机号",
     "password": "您的密码"
   }
   ```
3. 发送请求后，从响应中复制 `token` 值
4. 将该值保存到环境变量 `TOKEN` 中

## 问题相关 API

### 1. 创建问题

**请求信息**：
- 方法：`POST`
- URL：`{{BASE_URL}}/community/questions`
- 认证：Bearer Token (`{{TOKEN}}`)

**请求体**：
```json
{
  "title": "你更喜欢哪种颜色？",
  "background": "我正在设计一个产品，需要选择主色调",
  "content": "我有以下几个选择，你会选择哪个？",
  "options": [
    {
      "content": "蓝色"
    },
    {
      "content": "红色"
    },
    {
      "content": "绿色"
    }
  ],
  "isAnonymous": false,
  "requireReason": true,
  "visibility": "public",
  "expiryTime": "2023-12-31T23:59:59Z"
}
```

**如何测试**：
1. 在 Postman 中创建 POST 请求
2. 设置 URL 为 `{{BASE_URL}}/community/questions`
3. 在 Headers 标签页添加：`Authorization: Bearer {{TOKEN}}`
4. 在 Body 标签页选择 "raw" 和 "JSON"，粘贴上述 JSON
5. 点击发送，检查响应状态是否为 201 以及返回的问题 ID

### 2. 获取问题列表

**请求信息**：
- 方法：`GET`
- URL：`{{BASE_URL}}/community/questions`
- 认证：Bearer Token (`{{TOKEN}}`)

**查询参数**：
- `page`: 页码(默认 1)
- `limit`: 每页数量(默认 10)
- `sortBy`: 排序方式(newest/hottest/expiringSoon)
- `status`: 问题状态(open/closed)
- `userId`: 用户 ID(查看特定用户的问题)

**如何测试**：
1. 创建 GET 请求
2. 设置 URL 为 `{{BASE_URL}}/community/questions?page=1&limit=10&sortBy=newest`
3. 添加 Authorization 头部
4. 发送请求并检查返回的问题列表

### 3. 获取问题详情

**请求信息**：
- 方法：`GET`
- URL：`{{BASE_URL}}/community/questions/{id}`
- 认证：Bearer Token (`{{TOKEN}}`)

**如何测试**：
1. 将 `{id}` 替换为之前创建的问题 ID
2. 设置 Authorization 头部
3. 发送请求并检查问题详情

### 4. 更新问题

**请求信息**：
- 方法：`PUT`
- URL：`{{BASE_URL}}/community/questions/{id}`
- 认证：Bearer Token (`{{TOKEN}}`)

**请求体**：
```json
{
  "title": "更新后的问题标题",
  "background": "更新后的背景信息",
  "content": "更新后的内容",
  "options": [
    {
      "content": "选项 A"
    },
    {
      "content": "选项 B"
    }
  ]
}
```

**如何测试**：
1. 将 `{id}` 替换为要更新的问题 ID
2. 设置 Authorization 头部
3. 在 Body 标签页输入更新的信息
4. 发送请求并检查更新是否成功

**注意**：只有问题发布者且问题未收到回答时才能更新

### 5. 删除问题

**请求信息**：
- 方法：`DELETE`
- URL：`{{BASE_URL}}/community/questions/{id}`
- 认证：Bearer Token (`{{TOKEN}}`)

**如何测试**：
1. 将 `{id}` 替换为要删除的问题 ID
2. 设置 Authorization 头部
3. 发送请求并检查删除是否成功

**注意**：只有问题发布者且问题未收到回答时才能删除

## 回答相关 API

### 1. 提交回答

**请求信息**：
- 方法：`POST`
- URL：`{{BASE_URL}}/community/questions/{questionId}/answers`
- 认证：Bearer Token (`{{TOKEN}}`)

**请求体**：
```json
{
  "optionId": "60d21b4667d0d8992e610c85",
  "content": "我选择蓝色是因为它给人宁静的感觉",
  "isAnonymous": false
}
```

**如何测试**：
1. 将 `{questionId}` 替换为要回答的问题 ID
2. 将 `optionId` 替换为从问题详情 API 获取的选项 ID
3. 设置 Authorization 头部
4. 在 Body 标签页输入回答信息
5. 发送请求并检查回答是否提交成功

### 2. 获取问题的回答列表

**请求信息**：
- 方法：`GET`
- URL：`{{BASE_URL}}/community/questions/{questionId}/answers`
- 认证：Bearer Token (`{{TOKEN}}`)

**查询参数**：
- `page`: 页码(默认 1)
- `limit`: 每页数量(默认 10)
- `sortBy`: 排序方式(newest/mostLiked)
- `optionId`: 选项 ID(筛选特定选项的回答)

**如何测试**：
1. 将 `{questionId}` 替换为问题 ID
2. 设置查询参数，如 `?page=1&limit=10&sortBy=newest`
3. 设置 Authorization 头部
4. 发送请求并检查返回的回答列表

### 3. 点赞/取消点赞回答

**请求信息**：
- 方法：`POST`
- URL：`{{BASE_URL}}/community/answers/{id}/like`
- 认证：Bearer Token (`{{TOKEN}}`)

**请求体**：
```json
{
  "action": "like"  // 或 "unlike" 取消点赞
}
```

**如何测试**：
1. 将 `{id}` 替换为要点赞的回答 ID
2. 设置 Authorization 头部
3. 在 Body 标签页输入操作类型
4. 发送请求并检查点赞是否成功

## 评论相关 API

### 1. 添加评论

**请求信息**：
- 方法：`POST`
- URL：`{{BASE_URL}}/community/answers/{answerId}/comments`
- 认证：Bearer Token (`{{TOKEN}}`)

**请求体**：
```json
{
  "content": "非常赞同你的观点",
  "isAnonymous": false,
  "parentId": null  // 如果是回复其他评论，填写父评论 ID
}
```

**如何测试**：
1. 将 `{answerId}` 替换为要评论的回答 ID
2. 设置 Authorization 头部
3. 在 Body 标签页输入评论信息
4. 发送请求并检查评论是否发表成功

### 2. 获取评论列表

**请求信息**：
- 方法：`GET`
- URL：`{{BASE_URL}}/community/answers/{answerId}/comments`
- 认证：Bearer Token (`{{TOKEN}}`)

**查询参数**：
- `page`: 页码(默认 1)
- `limit`: 每页数量(默认 20)

**如何测试**：
1. 将 `{answerId}` 替换为回答 ID
2. 设置查询参数，如 `?page=1&limit=20`
3. 设置 Authorization 头部
4. 发送请求并检查返回的评论列表

### 3. 删除评论

**请求信息**：
- 方法：`DELETE`
- URL：`{{BASE_URL}}/community/comments/{id}`
- 认证：Bearer Token (`{{TOKEN}}`)

**如何测试**：
1. 将 `{id}` 替换为要删除的评论 ID
2. 设置 Authorization 头部
3. 发送请求并检查删除是否成功

**注意**：只有评论发布者才能删除评论

## 常见错误及解决方法

### 401 Unauthorized
- 检查 TOKEN 是否正确设置
- TOKEN 可能已过期，需要重新登录获取新 TOKEN

### 400 Bad Request
- 检查请求参数是否符合要求
- 检查请求体格式是否正确

### 403 Forbidden
- 您可能没有权限执行该操作（例如，尝试更新/删除不属于您的问题）

### 404 Not Found
- 检查 URL 中的 ID 是否正确
- 确认请求的资源存在

## 测试流程示例

以下是一个完整的测试流程示例：

1. 登录获取 TOKEN
2. 创建一个新问题
3. 获取问题列表，确认新问题已创建
4. 获取问题详情
5. 对问题提交回答
6. 获取回答列表
7. 点赞某个回答
8. 对回答添加评论
9. 获取评论列表
10. 删除评论

按照这个流程依次测试，可以全面验证社区模块 API 的功能。

## 总结

本文档涵盖了"听听"微信小程序社区模块的所有 API 测试方法。通过使用 Postman 及设置的环境变量，您可以方便地测试各个 API 的功能。如有任何问题，请参考错误处理部分或联系开发团队。
