/**
 * 通知角标组件
 * 显示未读通知数量的小红点
 */
const app = getApp();

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 角标数字
    count: {
      type: Number,
      value: 0
    },
    // 是否显示红点
    showDot: {
      type: Boolean,
      value: false
    },
    // 图标大小
    size: {
      type: Number,
      value: 48
    },
    // 图标颜色
    color: {
      type: String,
      value: '#333333'
    },
    // 选中状态
    active: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    unreadCount: 0
  },

  lifetimes: {
    attached: function() {
      // 初始化未读数量
      this.setData({
        unreadCount: this.properties.count
      });
      
      // 监听未读通知数量更新事件
      app.getEventChannel().on('unreadCountUpdated', this.handleUnreadCountUpdated.bind(this));
    },
    
    detached: function() {
      // 取消事件监听
      app.getEventChannel().off('unreadCountUpdated', this.handleUnreadCountUpdated);
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理未读通知数量更新
     * @param {Number} count 更新后的数量
     */
    handleUnreadCountUpdated: function(count) {
      this.setData({
        unreadCount: count
      });
    },
    
    /**
     * 点击图标
     */
    onClick: function() {
      this.triggerEvent('click');
    }
  }
}) 