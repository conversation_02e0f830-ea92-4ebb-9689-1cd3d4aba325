先用mongosh进入到数据库
// 查看所有数据库
show dbs

// 切换到你的数据库
use xuanxuan

// 查看所有集合（表）
show collections

// 查看用户数据
db.users.find()

// 查看投票数据
db.polls.find()

// 查看评论数据
db.comments.find()

// 只看前5条数据
db.users.find().limit(5)

// 格式化显示
db.users.find().pretty()

// ===== 删除操作 =====
// 清空某个集合的所有数据（保留集合结构）
db.users.deleteMany({})
db.polls.deleteMany({})
db.comments.deleteMany({})

// 删除整个集合（包括集合本身）
db.users.drop()
db.polls.drop()

// 删除符合条件的文档
db.users.deleteOne({_id: ObjectId("具体的ID")})
db.users.deleteMany({status: "inactive"})

// ===== 插入数据 =====
// 插入单个文档
db.users.insertOne({name: "测试用户", email: "<EMAIL>"})

// 插入多个文档
db.users.insertMany([
  {name: "用户1", email: "<EMAIL>"},
  {name: "用户2", email: "<EMAIL>"}
])

// ===== 更新数据 =====
// 更新单个文档
db.users.updateOne(
  {_id: ObjectId("具体的ID")},
  {$set: {name: "新名称", updatedAt: new Date()}}
)

// 更新多个文档
db.users.updateMany(
  {status: "pending"},
  {$set: {status: "active", updatedAt: new Date()}}
)

// ===== 查询操作 =====
// 条件查询
db.users.find({status: "active"})
db.users.find({age: {$gte: 18}})  // 年龄大于等于18
db.users.find({name: /张/})       // 名字包含"张"

// 排序查询
db.users.find().sort({createdAt: -1})  // 按创建时间降序
db.users.find().sort({name: 1})        // 按名字升序

// 分页查询
db.users.find().skip(10).limit(5)  // 跳过10条，取5条

// 只返回指定字段
db.users.find({}, {name: 1, email: 1, _id: 0})

// ===== 统计操作 =====
// 统计集合中文档数量
db.users.countDocuments()
db.users.countDocuments({status: "active"})

// 聚合统计
db.users.aggregate([
  {$group: {_id: "$status", count: {$sum: 1}}}
])

// ===== 索引操作 =====
// 查看集合的索引
db.users.getIndexes()

// 创建索引
db.users.createIndex({email: 1})      // 单字段索引
db.users.createIndex({name: 1, age: -1})  // 复合索引

// 删除索引
db.users.dropIndex({email: 1})
db.users.dropIndexes()  // 删除所有索引（除了_id）

// ===== 其他常用命令 =====
// 查看数据库状态
db.stats()

// 查看集合状态
db.users.stats()

// 重命名集合
db.users.renameCollection("new_users")

// 查看当前数据库
db.getName()

// 退出mongosh
exit