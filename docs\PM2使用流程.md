# 选选项目 PM2 使用流程文档

## 📋 目录
- [项目概述](#项目概述)
- [环境准备](#环境准备)
- [脚本使用方法](#脚本使用方法)
- [开机自启设置](#开机自启设置)
- [常用命令详解](#常用命令详解)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 📖 项目概述

选选项目使用 PM2 进行生产环境的进程管理，通过自定义的 `pm2-scripts.sh` 脚本简化常用操作。

### 相关文件
- `server/pm2-scripts.sh` - PM2 管理脚本
- `server/ecosystem.config.js` - PM2 配置文件
- `server/logs/` - 日志目录
- `server/pids/` - 进程ID文件目录

## 🔧 环境准备

### 1. 安装 Node.js 和 PM2
```bash
# 安装 Node.js (建议使用 LTS 版本)
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# 全局安装 PM2
npm install -g pm2
```

### 2. 验证安装
```bash
node --version
npm --version
pm2 --version
```

## 🚀 脚本使用方法

### 首次部署（推荐使用）
```bash
# 进入服务器目录
cd server

# 给脚本执行权限
chmod +x pm2-scripts.sh

# 一键初始化和启动（创建目录 + 安装依赖 + 启动服务）
./pm2-scripts.sh setup
```

### 日常操作命令

#### 启动服务
```bash
./pm2-scripts.sh start
```

#### 停止服务
```bash
./pm2-scripts.sh stop
```

#### 重启服务
```bash
./pm2-scripts.sh restart
```

#### 零停机重新加载
```bash
./pm2-scripts.sh reload
```

#### 查看服务状态
```bash
./pm2-scripts.sh status
```

#### 查看日志
```bash
./pm2-scripts.sh logs
```

#### 打开监控面板
```bash
./pm2-scripts.sh monit
```

#### 删除进程
```bash
./pm2-scripts.sh delete
```

## 🔄 开机自启设置

### 步骤一：生成启动脚本
```bash
# 生成系统启动脚本
pm2 startup
```

**执行后会输出类似以下命令，需要复制执行：**
```bash
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u yourusername --hp /home/<USER>
```

### 步骤二：启动应用并保存
```bash
# 启动应用（如果还没启动）
./pm2-scripts.sh start

# 保存当前进程列表到 PM2
pm2 save
```

### 步骤三：验证设置
```bash
# 查看已保存的应用
pm2 list

# 检查启动脚本状态
systemctl status pm2-yourusername

# 测试重启（可选，请谨慎操作）
sudo reboot
```

### 取消开机自启（如需要）
```bash
pm2 unstartup systemd
```

## 📝 常用命令详解

### PM2 原生命令

#### 进程管理
```bash
# 查看所有进程
pm2 list

# 查看详细信息
pm2 describe xuanxuan-server

# 监控进程
pm2 monit

# 重新加载所有应用
pm2 reload all
```

#### 日志管理
```bash
# 查看实时日志
pm2 logs xuanxuan-server

# 查看错误日志
pm2 logs xuanxuan-server --err

# 清空日志
pm2 flush

# 旋转日志文件
pm2 install pm2-logrotate
```

#### 进程信息
```bash
# 查看进程详情
pm2 show xuanxuan-server

# 查看进程ID
pm2 pid xuanxuan-server
```

## 📊 监控和日志

### 日志文件位置
```
server/logs/
├── xuanxuan-combined.log  # 合并日志（包含输出和错误）
├── xuanxuan-out.log       # 标准输出日志
└── xuanxuan-error.log     # 错误日志
```

### 实时监控
```bash
# 打开 PM2 监控面板
./pm2-scripts.sh monit

# 或使用原生命令
pm2 monit
```

### Web 监控（可选）
```bash
# 安装 PM2 Plus（免费版）
pm2 install pm2-server-monit
```

## 🔧 故障排除

### 常见问题

#### 1. 权限问题
```bash
# 脚本无执行权限
chmod +x pm2-scripts.sh

# PM2 权限问题
sudo chown -R $USER:$USER ~/.pm2
```

#### 2. 端口被占用
```bash
# 查看端口占用
lsof -i :3000
netstat -tlnp | grep :3000

# 杀死占用端口的进程
kill -9 <PID>
```

#### 3. 应用无法启动
```bash
# 查看详细错误信息
pm2 logs xuanxuan-server --err

# 检查配置文件
node -c ecosystem.config.js

# 手动启动测试
cd server/src && node index.js
```

#### 4. 内存泄漏
```bash
# 设置内存限制重启
# 在 ecosystem.config.js 中已配置：
# max_memory_restart: '1500M'
```

### 日志分析
```bash
# 查看最近的错误日志
tail -f server/logs/xuanxuan-error.log

# 查看启动日志
tail -f server/logs/xuanxuan-out.log

# 搜索特定错误
grep -i "error" server/logs/xuanxuan-combined.log
```

## 📚 最佳实践

### 1. 生产环境配置建议
- 关闭 `watch` 模式以提高性能
- 设置合理的内存重启阈值
- 配置日志轮转避免日志文件过大
- 设置最大重启次数避免无限重启

### 2. 监控建议
- 定期检查应用状态：`pm2 status`
- 监控系统资源使用：`pm2 monit`
- 设置日志告警机制

### 3. 更新部署流程
```bash
# 1. 备份当前版本
cp -r server server_backup_$(date +%Y%m%d_%H%M%S)

# 2. 更新代码
git pull origin main

# 3. 安装依赖
npm install --production

# 4. 零停机重新加载
./pm2-scripts.sh reload

# 5. 验证服务状态
./pm2-scripts.sh status
```

### 4. 定期维护
```bash
# 清理日志（每周执行）
pm2 flush

# 重启应用（每月执行，可选）
./pm2-scripts.sh restart

# 更新 PM2
npm update -g pm2
pm2 update
```

## 🚨 紧急操作

### 快速重启
```bash
./pm2-scripts.sh restart
```

### 紧急停机
```bash
./pm2-scripts.sh stop
```

### 查看实时日志排查问题
```bash
./pm2-scripts.sh logs
```

### 完全重新部署
```bash
./pm2-scripts.sh delete
./pm2-scripts.sh setup
```

## 📞 技术支持

如遇到问题，请按以下顺序排查：
1. 检查应用状态：`./pm2-scripts.sh status`
2. 查看错误日志：`./pm2-scripts.sh logs`
3. 检查系统资源：`pm2 monit`
4. 重启应用：`./pm2-scripts.sh restart`

---

**最后更新：** $(date +%Y-%m-%d)  
**文档版本：** v1.0  
**维护人员：** 选选项目开发团队
