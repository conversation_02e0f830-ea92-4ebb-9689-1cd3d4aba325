/* AI推荐产品选项弹窗样式 */
.ai-recommend-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.ai-recommend-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.ai-recommend-modal.show .modal-content {
  transform: translateY(0);
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999;
  border-radius: 50%;
  background-color: #f8f8f8;
}

/* 弹窗主体 */
.modal-body {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 32rpx 24rpx;
}

.form-section {
  padding: 24rpx 0;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.section-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.required {
  color: #ff4757;
}

.form-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}

/* 产品类型选择 */
.product-type-options {
  display: flex;
  gap: 16rpx;
}

.type-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  background-color: #fff;
  transition: all 0.2s ease;
}

.type-option.active {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.option-check {
  font-size: 24rpx;
  color: #007aff;
  font-weight: bold;
}

/* 选中的产品类别显示 */
.selected-category {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #007aff 0%, #005ce6 100%);
  border-radius: 16rpx;
  margin-bottom: 8rpx;
}

.category-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 品牌选择 */
.brand-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.brand-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.brand-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 32rpx;
  background-color: #fff;
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.brand-item.selected {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.brand-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 8rpx;
}

.brand-check {
  font-size: 20rpx;
  color: #007aff;
  font-weight: bold;
}

.brand-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 存储容量选择 */
.storage-container {
  width: 100%;
}

.storage-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  background-color: #fff;
}

.placeholder {
  font-size: 28rpx;
  color: #999;
}

.selected-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #666;
}

/* 已选择存储容量标签 */
.selected-storage-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
}

.storage-tag {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #007aff;
  border-radius: 32rpx;
  color: #fff;
}

.tag-text {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.tag-remove {
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
}

/* 弹窗底部 */
.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.confirm-btn {
  flex: 2;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #007aff 0%, #005ce6 100%);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.confirm-btn.loading {
  background: #ccc;
  color: #999;
}

.confirm-btn.disabled {
  background: #e8e8e8;
  color: #ccc;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  color: #666;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.cancel-btn:active {
  background-color: #e8e8e8;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .modal-content {
    max-height: 85vh;
  }
  
  .modal-body {
    max-height: 65vh;
  }
  
  .brand-item {
    min-width: 100rpx;
    padding: 12rpx 20rpx;
  }
  
  .brand-text {
    font-size: 24rpx;
  }
}

/* ==================== AI处理状态样式 ==================== */
.processing-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  text-align: center;
}

.processing-animation {
  margin-bottom: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-message {
  font-size: 32rpx;
  color: #007aff;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.processing-tip {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* ==================== 表单样式优化 ==================== */
.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.product-types {
  display: flex;
  gap: 20rpx;
}

.type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  background: #fff;
  transition: all 0.2s ease;
}

.type-option.active {
  border-color: #007aff;
  background: #f8fbff;
}

.type-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.type-text {
  font-size: 26rpx;
  color: #333;
}

.brand-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.brand-item {
  position: relative;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 32rpx;
  background: #fff;
  transition: all 0.2s ease;
}

.brand-item.selected {
  border-color: #007aff;
  background: #f8fbff;
}

.brand-text {
  font-size: 26rpx;
  color: #333;
}

.brand-check {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 20rpx;
  height: 20rpx;
  background: #007aff;
  color: #fff;
  border-radius: 50%;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.storage-picker {
  margin-bottom: 20rpx;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fff;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

.selected-storage {
  margin-top: 20rpx;
}

.selected-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.storage-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.storage-tag {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
}

.tag-text {
  font-size: 24rpx;
  color: #333;
  margin-right: 8rpx;
}

.tag-remove {
  font-size: 24rpx;
  color: #999;
  cursor: pointer;
}

.tip-section {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8fbff;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.tip-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* ==================== 按钮样式优化 ==================== */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #007aff;
  color: #fff;
}

.confirm-btn.disabled {
  background: #ccc;
  color: #999;
}

.confirm-btn.active {
  background: #007aff;
  color: #fff;
}