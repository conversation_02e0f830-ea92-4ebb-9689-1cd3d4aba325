<!--components/product-compare-input/product-compare-input.wxml-->
<view class="compare-input-container">
  <!-- 对比面板切换按钮 -->
  <view class="compare-toggle-btn {{visible ? 'active' : ''}}" bindtap="togglePanel">
    <view class="toggle-icon">
      <text class="icon-compare">⚖️</text>
    </view>
    <view class="toggle-text">对比</view>
    <view class="compare-count" wx:if="{{compareProducts.length > 0}}">{{compareProducts.length}}</view>
  </view>

  <!-- 对比面板 -->
  <view class="compare-panel {{visible ? 'show' : 'hide'}}">
    <!-- 面板头部 -->
    <view class="panel-header">
      <view class="panel-title">
        <text class="title-icon">⚖️</text>
        <text class="title-text">产品对比</text>
        <text class="count-text">({{compareProducts.length}}/{{maxCompareCount}})</text>
      </view>
      <view class="panel-actions">
        <view class="action-btn clear-btn" bindtap="clearAll" wx:if="{{compareProducts.length > 0}}">
          <text class="action-icon">🗑️</text>
          <text class="action-text">清空</text>
        </view>
        <view class="action-btn close-btn" bindtap="togglePanel">
          <text class="action-icon">✕</text>
        </view>
      </view>
    </view>

    <!-- 对比产品列表 -->
    <scroll-view class="compare-products-scroll" scroll-y="{{true}}">
      <view class="compare-products-list">
        <!-- 已添加的对比产品 -->
        <view 
          class="compare-product-item"
          wx:for="{{compareProducts}}"
          wx:key="{{item.skuId || index}}"
          data-product="{{item}}"
          bindtap="onProductTap"
        >
          <view class="product-image-container">
            <image 
              class="product-image"
              src="{{item.imageUrl}}"
              mode="aspectFill"
              lazy-load="{{true}}"
              binderror="onImageError"
            ></image>
            <view 
              class="remove-btn"
              data-index="{{index}}"
              catchtap="removeProduct"
            >
              <text class="remove-icon">✕</text>
            </view>
          </view>
          <view class="product-name">{{item.skuName}}</view>
        </view>

        <!-- 空槽位 -->
        <view 
          class="compare-product-item empty-slot"
          wx:for="{{maxCompareCount - compareProducts.length}}"
          wx:key="empty-{{index}}"
          wx:if="{{compareProducts.length < maxCompareCount}}"
        >
          <view class="empty-slot-content">
            <text class="empty-icon">+</text>
            <text class="empty-text">添加产品</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 面板底部操作 -->
    <view class="panel-footer" wx:if="{{compareProducts.length > 0}}">
      <button 
        class="compare-btn {{compareProducts.length >= 2 ? 'enabled' : 'disabled'}}"
        bindtap="startCompare"
        disabled="{{compareProducts.length < 2}}"
      >
        传统对比 ({{compareProducts.length}})
      </button>
      <button 
        class="compare-btn-v4 {{compareProducts.length >= 2 ? 'enabled' : 'disabled'}}"
        bindtap="startCompareV4"
        disabled="{{compareProducts.length < 2}}"
      >
        <text class="btn-icon">🤖</text>
        <text class="btn-text">AI智能对比 ({{compareProducts.length}})</text>
      </button>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{compareProducts.length === 0}}">
      <view class="empty-icon">📱</view>
      <view class="empty-title">暂无对比产品</view>
      <view class="empty-desc">点击产品列表中的"加入对比"按钮添加产品</view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view class="compare-mask {{visible ? 'show' : 'hide'}}" bindtap="togglePanel"></view>
</view>