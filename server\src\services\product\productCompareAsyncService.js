const ProductComparisonTask = require('../../models/ProductComparisonTask');
const { compareProductsByNamesV4Parallel } = require('./productCompareParallel');
const notificationService = require('../notificationService');

/**
 * 产品对比异步服务
 * 处理异步产品对比任务的创建、执行和通知
 */

/**
 * 创建异步产品对比任务
 * @param {String} userId - 用户ID
 * @param {Array<String>} productNames - 产品名称列表
 * @returns {Promise<Object>} 任务创建结果
 */
const createComparisonTask = async (userId, productNames) => {
  try {
    console.log('🚀 创建异步产品对比任务:', { userId, productNames });

    // 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2 || productNames.length > 6) {
      throw new Error('产品名称列表必须包含2-6个产品');
    }

    // 创建任务记录
    const task = await ProductComparisonTask.createTask(userId, productNames);
    
    console.log('✅ 任务创建成功:', task.taskId);

    // 异步执行任务 (不等待结果)
    setImmediate(() => {
      executeComparisonTask(task._id).catch(error => {
        console.error('❌ 异步任务执行失败:', error);
      });
    });

    return {
      success: true,
      data: {
        taskId: task.taskId,
        status: task.status,
        message: 'AI分析已开始，完成后会通知您',
        estimatedTime: '预计1-2分钟完成'
      }
    };

  } catch (error) {
    console.error('❌ 创建异步任务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 执行产品对比任务
 * @param {String} taskObjectId - 任务对象ID
 */
const executeComparisonTask = async (taskObjectId) => {
  let task = null;
  
  try {
    // 获取任务信息
    task = await ProductComparisonTask.findById(taskObjectId);
    if (!task) {
      throw new Error('任务不存在');
    }

    console.log(`🔄 开始执行任务: ${task.taskId}`);

    // 更新任务状态为处理中
    await task.updateStatus('processing', { progress: 10 });

    // 执行产品对比分析
    console.log(`🤖 调用AI分析服务...`);
    await task.updateProgress(30);

    const result = await compareProductsByNamesV4Parallel(task.productNames, task.userId.toString());

    if (!result.success) {
      throw new Error(result.error || 'AI分析失败');
    }

    console.log(`✅ AI分析完成: ${task.taskId}`);
    await task.updateProgress(90);

    // 保存结果
    await task.setResult(result.data);

    // 发送完成通知
    await sendCompletionNotification(task);

    console.log(`🎉 任务完成: ${task.taskId}`);

  } catch (error) {
    console.error(`❌ 任务执行失败: ${task?.taskId || taskObjectId}`, error);
    
    if (task) {
      // 保存错误信息
      await task.setError(error);
      
      // 发送失败通知
      await sendFailureNotification(task, error);
    }
  }
};

/**
 * 发送任务完成通知
 * @param {Object} task - 任务对象
 */
const sendCompletionNotification = async (task) => {
  try {
    const content = `您的产品对比分析已完成！对比了 ${task.productNames.length} 个产品，点击查看详细结果。`;
    
    await notificationService.createNotification(
      task.userId,
      null, // 系统通知，无发送者
      'product_comparison_completed',
      content,
      task._id,
      'product_comparison_task',
      {
        taskId: task.taskId,
        productNames: task.productNames,
        processingTime: task.metadata.processingTime
      }
    );

    // 标记通知已发送
    task.notificationSent = true;
    await task.save();

    console.log(`📢 完成通知已发送: ${task.taskId}`);

  } catch (error) {
    console.error('❌ 发送完成通知失败:', error);
  }
};

/**
 * 发送任务失败通知
 * @param {Object} task - 任务对象
 * @param {Error} error - 错误对象
 */
const sendFailureNotification = async (task, error) => {
  try {
    const content = `很抱歉，您的产品对比分析失败了。请稍后重试或联系客服。`;
    
    await notificationService.createNotification(
      task.userId,
      null, // 系统通知，无发送者
      'product_comparison_failed',
      content,
      task._id,
      'product_comparison_task',
      {
        taskId: task.taskId,
        productNames: task.productNames,
        errorMessage: error.message
      }
    );

    // 标记通知已发送
    task.notificationSent = true;
    await task.save();

    console.log(`📢 失败通知已发送: ${task.taskId}`);

  } catch (notificationError) {
    console.error('❌ 发送失败通知失败:', notificationError);
  }
};

/**
 * 获取任务状态
 * @param {String} taskId - 任务ID
 * @param {String} userId - 用户ID
 * @returns {Promise<Object>} 任务状态信息
 */
const getTaskStatus = async (taskId, userId) => {
  try {
    const task = await ProductComparisonTask.findOne({ taskId, userId });
    
    if (!task) {
      return {
        success: false,
        error: '任务不存在或无权访问'
      };
    }

    return {
      success: true,
      data: {
        taskId: task.taskId,
        status: task.status,
        progress: task.progress,
        productNames: task.productNames,
        createdAt: task.createdAt,
        startedAt: task.startedAt,
        completedAt: task.completedAt,
        result: task.status === 'completed' ? task.result : null,
        error: task.status === 'failed' ? task.error : null,
        metadata: task.metadata
      }
    };

  } catch (error) {
    console.error('❌ 获取任务状态失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 获取用户的任务列表
 * @param {String} userId - 用户ID
 * @param {Number} page - 页码
 * @param {Number} limit - 每页数量
 * @returns {Promise<Object>} 任务列表
 */
const getUserTasks = async (userId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;
    
    const tasks = await ProductComparisonTask.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-result -error.stack') // 排除大字段
      .lean();
    
    const total = await ProductComparisonTask.countDocuments({ userId });
    
    return {
      success: true,
      data: {
        tasks,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      }
    };

  } catch (error) {
    console.error('❌ 获取用户任务列表失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  createComparisonTask,
  executeComparisonTask,
  getTaskStatus,
  getUserTasks
};
