/* components/feedback-item/feedback-item.wxss */

.feedback-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 0 0 16rpx 0;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.feedback-item:active {
  transform: scale(0.98);
  background-color: #fafafa;
}

/* 反馈头部 */
.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
  gap: 8rpx;
}

.type-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
  flex-shrink: 0;
}

.type-bug {
  background-color: #ff6b6b;
}

.type-suggestion {
  background-color: #4ecdc4;
}

.type-question {
  background-color: #45b7d1;
}

.type-other {
  background-color: #96ceb4;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 80rpx;
  text-align: center;
}

.status-pending {
  background-color: #ffeaa7;
  color: #d63031;
}

.status-processing {
  background-color: #81ecec;
  color: #00b894;
}

.status-resolved {
  background-color: #55a3ff;
  color: #fff;
}

.status-rejected {
  background-color: #fd79a8;
  color: #fff;
}

/* 反馈内容 */
.feedback-content {
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  word-break: break-all;
}

/* 图片预览 */
.feedback-images {
  display: flex;
  gap: 8rpx;
  margin-bottom: 16rpx;
  align-items: center;
}

.image-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.more-images {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: 500;
}

/* 反馈底部 */
.feedback-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.create-time {
  font-size: 26rpx;
  color: #999;
}

.reply-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.reply-icon {
  font-size: 24rpx;
}

.reply-text {
  font-size: 26rpx;
  color: #4ecdc4;
  font-weight: 500;
} 