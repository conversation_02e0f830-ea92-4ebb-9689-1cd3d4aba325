# 社区模块数据表设计

## 问题表(Question)

### 基本信息
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| title | String | 是 | - | 问题标题，最多50个字符 |
| background | String | 否 | - | 问题背景，最多1000个字符 |
| content | String | 是 | - | 问题主干，最多500个字符 |
| options | Array[Object] | 是 | - | 问题选项数组，至少2个选项 |
| options[].content | String | 是 | - | 选项内容，最多100个字符 |
| options[].voteCount | Number | 否 | 0 | 该选项的投票数 |
| userId | ObjectId | 是 | - | 问题发布者用户ID |
| isAnonymous | Boolean | 否 | false | 是否匿名发布 |
| requireReason | Boolean | 否 | false | 选择是否必须带理由 |

### 可见性设置
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| visibility.type | String | 否 | public | 可见范围类型：public(所有人可见)、filtered(筛选条件可见) |
| visibility.filters.gender | Array[String] | 否 | [] | 性别筛选，可选值：male(男)、female(女)、secret(保密) |
| visibility.filters.minAge | Number | 否 | null | 最小年龄限制 |
| visibility.filters.maxAge | Number | 否 | null | 最大年龄限制 |
| visibility.filters.regions | Array[String] | 否 | [] | 地区筛选，例如：["北京市", "上海市"] |
| visibility.filters.occupations | Array[String] | 否 | [] | 职业筛选，例如：["学生", "工程师"] |

### 问题状态与统计
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| expiryTime | Date | 否 | null | 问题截止时间，到期自动关闭 |
| status | String | 否 | open | 问题状态：open(开放)、closed(已关闭) |
| totalVotes | Number | 否 | 0 | 总投票数 |
| commentCount | Number | 否 | 0 | 评论总数 |

### 时间戳
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| createdAt | Date | 否 | 当前时间 | 创建时间(自动生成) |
| updatedAt | Date | 否 | 当前时间 | 更新时间(自动生成) |

### 索引说明
- userId: 常规索引，便于查询用户发布的问题
- status + createdAt: 复合索引，用于查询活跃问题并按时间排序
- visibility.type: 常规索引，快速筛选公开/受限问题
- expiryTime: 常规索引，便于系统定时关闭到期问题
- totalVotes: 常规索引，用于热门问题排序

### 问题管理
问题发布后的管理规则：
1. 未有人回答前，问题创建者可以修改或删除问题
2. 已有回答后，只能修改标题和背景，不能修改选项
3. 问题截止时间到期后自动关闭，不再接受新的回答
4. 问题创建者可以提前手动关闭问题

## 回答表(Answer)

### 基本信息
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| questionId | ObjectId | 是 | - | 所属问题ID |
| optionId | ObjectId | 是 | - | 选择的选项ID |
| userId | ObjectId | 是 | - | 回答用户ID |
| content | String | 否 | - | 理由内容，当问题requireReason为true时必填，最多500个字符 |
| isAnonymous | Boolean | 否 | false | 是否匿名 |

### 互动数据
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| likes | Number | 否 | 0 | 点赞数 |
| likedBy | Array[ObjectId] | 否 | [] | 点赞用户ID列表 |
| commentCount | Number | 否 | 0 | 评论数 |

### 时间戳
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| createdAt | Date | 否 | 当前时间 | 创建时间(自动生成) |
| updatedAt | Date | 否 | 当前时间 | 更新时间(自动生成) |

### 索引说明
- questionId: 常规索引，查询问题的所有回答
- userId: 常规索引，查询用户的所有回答
- questionId + optionId: 复合索引，统计选项投票
- likes: 常规索引，用于回答排序
- createdAt: 常规索引，用于时间排序

### 回答限制
1. 每个用户对同一问题只能回答一次
2. 问题关闭后不能继续回答
3. 回答发布后不可修改，只能删除重新回答（在问题未关闭情况下）

## 评论表(Comment)

### 基本信息
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| questionId | ObjectId | 是 | - | 所属问题ID |
| answerId | ObjectId | 是 | - | 所属回答ID |
| parentId | ObjectId | 否 | null | 父评论ID，用于回复评论 |
| userId | ObjectId | 是 | - | 评论用户ID |
| targetUserId | ObjectId | 否 | null | 被回复用户ID |
| content | String | 是 | - | 评论内容，最多200个字符 |
| isAnonymous | Boolean | 否 | false | 是否匿名评论 |

### 时间戳
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|-------|------|
| createdAt | Date | 否 | 当前时间 | 创建时间(自动生成) |
| updatedAt | Date | 否 | 当前时间 | 更新时间(自动生成) |

### 索引说明
- answerId: 常规索引，查询回答的所有评论
- parentId: 常规索引，查询子评论
- userId: 常规索引，查询用户的所有评论
- questionId: 常规索引，查询问题的所有评论

### 评论规则
1. 问题发布者可以评论任何回答
2. 回答者只能看到和回复问题发布者对自己回答的评论
3. 回答者之间不能相互评论
4. 评论发布后不可修改，只能删除

