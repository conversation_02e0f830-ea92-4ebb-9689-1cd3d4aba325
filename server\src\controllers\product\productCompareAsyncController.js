const productCompareAsyncService = require('../../services/product/productCompareAsyncService');
const { success, error } = require('../../utils/response');
const { compareProductsV4ParallelSchema } = require('../../validators/productvalidator');

/**
 * 异步产品对比控制器
 * 处理异步产品对比相关的API请求
 */

/**
 * @desc    创建异步产品对比任务
 * @route   POST /api/v1/products/compare-async
 * @access  Private
 */
const createComparisonTask = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = compareProductsV4ParallelSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productNames, userId } = value;

    console.log('🚀 创建异步产品对比任务，产品列表:', productNames);
    console.log('🔍 用户ID:', userId);

    // 调用服务层创建异步任务
    const result = await productCompareAsyncService.createComparisonTask(userId, productNames);

    if (!result.success) {
      console.error('❌ 创建异步任务失败:', result.error);
      return error(res, 400, result.error);
    }

    console.log('✅ 异步任务创建成功:', result.data.taskId);

    // 返回成功响应
    const responseData = {
      ...result.data,
      meta: {
        requestedProducts: productNames,
        productCount: productNames.length,
        processingMode: 'async',
        description: '异步产品对比任务已创建，完成后将通过通知告知结果'
      }
    };

    return success(res, 202, '异步产品对比任务已创建', responseData);

  } catch (err) {
    console.error('异步产品对比控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('产品名称列表')) {
      return error(res, 400, err.message);
    }

    return error(res, 500, '异步产品对比服务暂时不可用，请稍后重试');
  }
};

/**
 * @desc    获取任务状态
 * @route   GET /api/v1/products/compare-async/:taskId
 * @access  Private
 */
const getTaskStatus = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id; // 从认证中间件获取用户ID

    console.log('🔍 查询任务状态:', { taskId, userId });

    // 调用服务层获取任务状态
    const result = await productCompareAsyncService.getTaskStatus(taskId, userId);

    if (!result.success) {
      console.error('❌ 获取任务状态失败:', result.error);
      return error(res, 404, result.error);
    }

    console.log('✅ 任务状态查询成功:', result.data.status);

    return success(res, 200, '任务状态获取成功', result.data);

  } catch (err) {
    console.error('获取任务状态控制器错误:', err);
    return error(res, 500, '获取任务状态失败，请稍后重试');
  }
};

/**
 * @desc    获取用户的任务列表
 * @route   GET /api/v1/products/compare-async
 * @access  Private
 */
const getUserTasks = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const userId = req.user.id; // 从认证中间件获取用户ID

    console.log('🔍 查询用户任务列表:', { userId, page, limit });

    // 调用服务层获取用户任务列表
    const result = await productCompareAsyncService.getUserTasks(
      userId, 
      parseInt(page), 
      parseInt(limit)
    );

    if (!result.success) {
      console.error('❌ 获取用户任务列表失败:', result.error);
      return error(res, 400, result.error);
    }

    console.log('✅ 用户任务列表查询成功，任务数量:', result.data.tasks.length);

    return success(res, 200, '用户任务列表获取成功', result.data);

  } catch (err) {
    console.error('获取用户任务列表控制器错误:', err);
    return error(res, 500, '获取任务列表失败，请稍后重试');
  }
};

module.exports = {
  createComparisonTask,
  getTaskStatus,
  getUserTasks
};
