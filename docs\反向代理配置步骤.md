# 选选项目反向代理配置详细步骤

## 1. 为什么必须配置反向代理？

### 1.1 开发角度的必要性分析

**必须配置！** 原因如下：

#### 安全性问题
- **端口暴露风险**：直接暴露5000和5001端口会增加安全风险
- **SSL证书应用**：您已经配置了SSL证书，但只能应用在标准的443端口(HTTPS)和80端口(HTTP)上
- **微信小程序要求**：微信小程序在生产环境中**必须使用HTTPS**，不能直接访问HTTP端口

#### 技术实现问题
- **端口统一管理**：通过反向代理，可以让所有服务都通过标准的443端口访问
- **域名映射**：您的域名`x-xuan.com`默认指向80/443端口，不会自动转发到5000/5001端口
- **负载均衡**：未来如果需要扩展，反向代理可以提供负载均衡功能

#### 用户体验
- **统一访问入口**：用户只需要记住一个域名，不需要加端口号
- **更好的SEO**：搜索引擎更喜欢标准的HTTPS链接

## 2. 当前代码配置分析

根据您的代码，当前配置如下：

### 2.1 前端配置（miniprogram/app.js）
```javascript
// 开发环境
baseUrl: 'http://localhost:5000/api/v1'
socketUrl: 'ws://localhost:5001'

// 生产环境（注释状态）
// baseUrl: 'http://*************:5000/api/v1'
// socketUrl: 'ws://*************:5001'
```

### 2.2 后端配置
- **HTTP API服务**：运行在5000端口
- **WebSocket服务**：运行在5001端口

### 2.3 需要修改的配置
配置反向代理后，前端代码需要修改为：
```javascript
baseUrl: 'https://x-xuan.com/api/v1'
socketUrl: 'wss://x-xuan.com/socket'
```

## 3. 宝塔面板反向代理详细配置步骤

### 3.1 创建网站

1. **登录宝塔面板**
   - 访问您的宝塔面板地址
   - 输入用户名和密码登录

2. **创建新网站**
   - 点击左侧菜单 "网站"
   - 点击 "添加站点"
   - 填写以下信息：
     - 域名：`x-xuan.com`
     - 根目录：可以选择一个空目录，例如 `/www/wwwroot/x-xuan.com`
     - PHP版本：选择 "纯静态"
   - 点击 "提交"

### 3.2 配置SSL证书

1. **进入网站设置**
   - 在网站列表中找到 `x-xuan.com`
   - 点击 "设置"

2. **配置SSL**
   - 点击 "SSL" 选项卡
   - 选择您已经申请的SSL证书
   - 点击 "保存"
   - **重要**：开启 "强制HTTPS" 选项

### 3.3 配置反向代理

1. **进入反向代理设置**
   - 在网站设置中，点击 "反向代理" 选项卡
   - 点击 "添加反向代理"

2. **配置API服务代理**
   - **代理名称**：`API服务`
   - **目标URL**：`http://127.0.0.1:5000`
   - **发送域名**：`$host`
   - **代理目录**：`/api/`
   - **高级设置**：
     ```nginx
     proxy_set_header Host $host;
     proxy_set_header X-Real-IP $remote_addr;
     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
     proxy_set_header X-Forwarded-Proto $scheme;
     ```
   - 点击 "提交"

3. **配置WebSocket服务代理**
   - 再次点击 "添加反向代理"
   - **代理名称**：`WebSocket服务`
   - **目标URL**：`http://127.0.0.1:5001`
   - **发送域名**：`$host`
   - **代理目录**：`/socket`
   - **高级设置**：
     ```nginx
     proxy_set_header Host $host;
     proxy_set_header X-Real-IP $remote_addr;
     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
     proxy_set_header X-Forwarded-Proto $scheme;
     
     # WebSocket 特殊配置
     proxy_http_version 1.1;
     proxy_set_header Upgrade $http_upgrade;
     proxy_set_header Connection "upgrade";
     proxy_read_timeout 86400;
     ```
   - 点击 "提交"

### 3.4 手动配置Nginx（如果自动配置不生效）

如果通过宝塔面板配置不生效，可以手动编辑Nginx配置：

1. **编辑配置文件**
   - 在网站设置中点击 "配置文件"
   - 或者通过 "文件" -> "/www/server/panel/vhost/nginx/" -> "x-xuan.com.conf"

2. **完整配置示例**
   ```nginx
   server {
       listen 80;
       server_name x-xuan.com;
       return 301 https://$server_name$request_uri;
   }

   server {
       listen 443 ssl http2;
       server_name x-xuan.com;
       
       # SSL证书配置
       ssl_certificate /www/server/panel/vhost/cert/x-xuan.com/fullchain.pem;
       ssl_certificate_key /www/server/panel/vhost/cert/x-xuan.com/privkey.pem;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers HIGH:!aNULL:!MD5;
       
       # API服务反向代理
       location /api/ {
           proxy_pass http://127.0.0.1:5000/api/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
       
       # WebSocket服务反向代理
       location /socket {
           proxy_pass http://127.0.0.1:5001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_read_timeout 86400;
       }
       
       # 默认页面（可选）
       location / {
           root /www/wwwroot/x-xuan.com;
           index index.html;
       }
   }
   ```

3. **重启Nginx**
   - 在宝塔面板中点击 "软件商店"
   - 找到 "Nginx"
   - 点击 "重启"

## 4. 修改前端代码

配置完反向代理后，需要修改小程序前端代码：

### 4.1 修改 miniprogram/app.js
```javascript
globalData: {
  // 开发环境
  // baseUrl: 'http://localhost:5000/api/v1',
  // socketUrl: 'ws://localhost:5001',
  
  // 生产环境
  baseUrl: 'https://x-xuan.com/api/v1',
  socketUrl: 'wss://x-xuan.com/socket',
  
  // 其他配置保持不变...
}
```

### 4.2 为什么这样修改？
- `https://x-xuan.com/api/v1` → 通过反向代理转发到内部的 `http://127.0.0.1:5000/api/v1`
- `wss://x-xuan.com/socket` → 通过反向代理转发到内部的 `ws://127.0.0.1:5001`
- 使用 `wss://` 而不是 `ws://`，因为这是基于HTTPS的安全WebSocket连接

## 5. 测试和验证

### 5.1 测试API接口
```bash
# 在浏览器或使用curl测试
curl https://x-xuan.com/api/v1/test
```

### 5.2 测试WebSocket连接
可以使用在线WebSocket测试工具，连接地址：
```
wss://x-xuan.com/socket?token=your_test_token
```

### 5.3 检查SSL证书
在浏览器中访问 `https://x-xuan.com`，查看地址栏是否显示安全锁图标。

## 6. 常见问题和解决方案

### 6.1 502 Bad Gateway错误
**原因**：后端服务未启动或端口被占用
**解决**：
```bash
# 检查服务状态
pm2 list
# 重启服务
pm2 restart all
```

### 6.2 WebSocket连接失败
**原因**：WebSocket代理配置不正确
**解决**：检查Nginx配置中的WebSocket相关设置，确保包含 `Upgrade` 和 `Connection` 头。

### 6.3 CORS跨域问题
**原因**：反向代理后域名变化导致跨域
**解决**：在后端代码中正确配置CORS，允许您的域名。

## 7. 配置完成后的访问地址

- **API接口**：`https://x-xuan.com/api/v1/...`
- **WebSocket**：`wss://x-xuan.com/socket`
- **网站首页**：`https://x-xuan.com`

## 8. 安全建议

1. **防火墙设置**：关闭5000和5001端口的外网访问，只允许内网访问
2. **定期更新**：定期更新SSL证书
3. **日志监控**：开启Nginx访问日志和错误日志监控
4. **备份配置**：定期备份Nginx配置文件

---

**配置完成后，您的微信小程序就可以通过HTTPS安全访问后端服务了！**
