/**
 * 智能产品搜索输入组件样式
 * Product Search Input Component Styles
 */

/* ==================== 组件容器 Component Container ==================== */

.product-search-input {
  position: relative;
  width: 100%;
  z-index: 1;
}

.product-search-input.focused {
  z-index: 99998;
  isolation: isolate;
}

/* ==================== 输入框容器 Input Container ==================== */

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  height: 88rpx;
  transition: all 0.3s ease;
}

.product-search-input.focused .search-input-container {
  border-color: #3B7ADB;
  box-shadow: 0 0 0 6rpx rgba(59, 122, 219, 0.1);
}

/* ==================== 搜索图标 Search Icon ==================== */

.search-icon {
  flex-shrink: 0;
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 32rpx;
}

.product-search-input.focused .search-icon {
  color: #3B7ADB;
}

/* ==================== 输入框 Input Field ==================== */

.search-input {
  flex: 1;
  height: 100%;
  font-size: 30rpx;
  color: #1f2937;
  background: transparent;
  border: none;
  outline: none;
}

.search-input::-webkit-input-placeholder {
  color: #9ca3af;
}

/* ==================== 加载指示器 Loading Indicator ==================== */

.loading-indicator {
  flex-shrink: 0;
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 28rpx;
  height: 28rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #3B7ADB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 清除按钮 Clear Button ==================== */

.clear-btn {
  flex-shrink: 0;
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 28rpx;
  border-radius: 50%;
  background: #f3f4f6;
  transition: all 0.2s ease;
}

.clear-btn:active {
  transform: scale(0.9);
  background: #e5e7eb;
  color: #6b7280;
}

/* ==================== 候选列表下拉框 Suggestions Dropdown ==================== */

.suggestions-dropdown {
  /* 默认使用绝对定位，相对于输入框容器 */
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 99999;
  background: #ffffff;
  border: 2rpx solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 12rpx 12rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15), 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  max-height: 360rpx;
  overflow: hidden;
  transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* 添加过渡效果 */
  transition: opacity 0.2s ease, transform 0.2s ease;
  /* 优化触摸体验 */
  touch-action: manipulation;
  -webkit-overflow-scrolling: touch;
  /* 防止触摸穿透 */
  pointer-events: auto;
  /* 确保触摸事件正常工作 */
  user-select: none;
  -webkit-user-select: none;
}

/* 当设置了内联样式时，会覆盖默认的定位方式 */
.suggestions-dropdown[style*="position: fixed"] {
  /* 固定定位时的额外样式调整 */
  min-width: 300rpx;
  max-width: 90vw;
  border-radius: 12rpx;
  border-top: 2rpx solid #e2e8f0;
}

.suggestions-container {
  max-height: 360rpx;
  /* 优化滚动体验 */
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
  /* 防止滚动时的橡皮筋效果影响触摸检测 */
  overscroll-behavior: contain;
}

/* ==================== 搜索建议列表 Suggestions List ==================== */

.suggestions-list {
  padding: 8rpx 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 24rpx;
  border-bottom: 1rpx solid #f1f5f9;
  transition: background-color 0.2s ease;
  position: relative;
  min-height: 96rpx;
  cursor: pointer;
  /* 优化触摸体验 */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  /* 增加触摸反馈 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: #f8fafc;
  /* 增强点击反馈 */
  transform: scale(0.98);
  transition: all 0.1s ease;
}

.suggestion-item:hover {
  background-color: #f1f5f9;
}

/* 增加触摸状态的视觉反馈 */
.suggestion-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 122, 219, 0.1);
  opacity: 0;
  transition: opacity 0.15s ease;
  pointer-events: none;
}

.suggestion-item:active::before {
  opacity: 1;
}

/* ==================== 产品图标 Product Icon ==================== */

.suggestion-icon {
  flex-shrink: 0;
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #ffffff;
}

/* ==================== 产品信息 Product Info ==================== */

.suggestion-info {
  flex: 1;
  min-width: 0;
}

.suggestion-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* ==================== 选择箭头 Selection Arrow ==================== */

.suggestion-arrow {
  flex-shrink: 0;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 24rpx;
  transition: color 0.2s ease;
}

.suggestion-item:hover .suggestion-arrow {
  color: #6b7280;
}

/* ==================== 无搜索结果 No Results ==================== */

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.no-results-icon {
  font-size: 80rpx;
  color: #d1d5db;
  margin-bottom: 20rpx;
}

.no-results-text {
  font-size: 30rpx;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.no-results-tip {
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.5;
}

/* ==================== 搜索中状态 Searching State ==================== */

.searching-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  text-align: center;
}

.searching-icon {
  margin-bottom: 20rpx;
}

.searching-text {
  font-size: 28rpx;
  color: #6b7280;
}

/* ==================== 响应式设计 Responsive Design ==================== */

/* 小屏幕设备优化 */
@media (max-width: 750rpx) {
  .suggestions-dropdown[style*="position: fixed"] {
    max-width: 95vw;
    min-width: 280rpx;
  }
  
  .suggestion-item {
    padding: 20rpx 16rpx;
    min-height: 80rpx;
  }
  
  .suggestion-name {
    font-size: 28rpx;
  }
  
  .suggestion-icon {
    width: 40rpx;
    height: 40rpx;
    font-size: 24rpx;
  }
}

/* ==================== 动画效果 Animations ==================== */

/* 下拉框显示动画 */
.suggestions-dropdown {
  animation: dropdown-show 0.2s ease-out;
}

@keyframes dropdown-show {
  0% {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 无障碍支持 Accessibility ==================== */

.suggestion-item:focus {
  outline: 2rpx solid #3B7ADB;
  outline-offset: -2rpx;
}

/* ==================== 深色模式支持 Dark Mode Support ==================== */

@media (prefers-color-scheme: dark) {
  .suggestions-dropdown {
    background: #1f2937;
    border-color: #374151;
  }
  
  .suggestion-item {
    border-bottom-color: #374151;
  }
  
  .suggestion-item:active,
  .suggestion-item:hover {
    background-color: #374151;
  }
  
  .suggestion-name {
    color: #f9fafb;
  }
  
  .suggestion-arrow {
    color: #9ca3af;
  }
  
  .no-results-text,
  .searching-text {
    color: #d1d5db;
  }
  
  .no-results-tip {
    color: #9ca3af;
  }
} 