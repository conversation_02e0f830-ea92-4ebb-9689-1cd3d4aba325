/**
 * ProductService V4 并行版本测试脚本
 * 测试基于参数字段提取的智能产品对比功能（并行处理） - JSON格式输出
 * 功能: 从productCompareParallel获取AI分析结果并保存原始JSON数据到文件
 * 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro
 * 重点测试: 获取原始JSON数据，不进行额外的参数检测和数据处理
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { compareProductsByNamesV4Parallel } = require('../src/services/product/productCompareParallel');

// 数据库连接配置
const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error.message);
  }
}

/**
 * 测试产品对比功能 - V4并行版本
 */
async function testProductComparisonV4Parallel() {
  console.log('\n🆚 测试: 产品对比功能 V4 并行版本 (华为 Mate 70 Pro vs 苹果iPhone 16 Pro)');
  console.log('='.repeat(80));

  const productNames = [
    '华为 Mate 70 Pro',       // 使用数据库中实际存在的产品名称
    '苹果iPhone 16 Pro'       // 使用数据库中实际存在的产品名称
  ];

  console.log(`📱 对比产品: ${productNames.join(' vs ')}`);
  console.log(`💡 注意: 并行版测试，仅获取和保存原始JSON数据`);

  try {
    const startTime = Date.now();

    const result = await compareProductsByNamesV4Parallel(productNames);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`⏱️ 对比耗时: ${duration}ms`);

    if (result.success) {
      console.log('\n✅ 产品对比成功!');

      // 保存原始JSON数据到文件
      try {
        const v4FileName = 'v4_parallel.json';
        const v4FilePath = path.join(__dirname, v4FileName);

        fs.writeFileSync(v4FilePath, JSON.stringify(result, null, 2), 'utf8');
        console.log(`\n📄 V4并行版本原始JSON数据已保存到文件: ${v4FilePath}`);
        console.log(`📂 文件大小: ${fs.statSync(v4FilePath).size} 字节`);

        console.log('\n✅ 原始JSON数据保存完成，无需进行额外的参数检测和数据处理');

      } catch (saveError) {
        console.error(`❌ 保存文件失败: ${saveError.message}`);
      }

    } else {
      console.log(`❌ 产品对比失败: ${result.error}`);
    }

  } catch (error) {
    console.error(`❌ 测试执行失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  await connectDB();
  await testProductComparisonV4Parallel();
  await disconnectDB();
}

main();