const NewProduct = require('../../models/NewProduct');
const ProductComparisonV4Cache = require('../../models/ProductComparisonV4Cache');
const axios = require('axios');

/**
 * 产品服务 V4 - 基于参数字段提取的智能对比
 * 新增功能：
 * 1. 自动提取产品参数字段和类型
 * 2. 明确指导AI分析特定参数
 * 3. 确保参数分析的全面性和一致性
 */

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.4,
  maxTokens: 8000, // 调整token限制以避免API 400错误
  timeout: 180000  // 增加超时时间
};

/**
 * V4版本完全基于实际产品数据动态提取参数，不再依赖硬编码的参数映射
 * 这样可以确保：
 * 1. 适应数据结构的变化
 * 2. 自动发现新的参数字段
 * 3. 避免参数遗漏
 * 4. 支持任意产品类型
 */

/**
 * 调用DeepSeek API
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`🤖 正在调用DeepSeek AI进行智能产品分析...`);
    console.log("userPrompt:", userPrompt);
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`✅ DeepSeek AI分析完成`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI分析失败: ${error.message}`);
  }
}

/**
 * 提取产品参数字段和类型信息
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Object} 参数分析结果
 */
function extractProductParametersAndTypes(formattedProducts) {
  console.log('🔍 开始提取产品参数字段和类型信息...');
  
  // 1. 分析产品类型
  const productTypes = [...new Set(formattedProducts.map(p => p.productType))];
  const primaryProductType = productTypes[0]; // 使用第一个产品类型作为主要类型
  const isMixedTypes = productTypes.length > 1;
  
  console.log(`📋 检测到产品类型: ${productTypes.join(', ')}`);
  
  // 2. 收集所有参数字段
  const allParameters = new Set();
  const parametersByCategory = {};
  const parameterValues = {}; // 存储每个参数在不同产品中的值
  
  formattedProducts.forEach((product, index) => {
    console.log(`📊 分析产品 ${index + 1}: ${product.name}`);
    
    // 分析 commonSpecs
    if (product.commonSpecs) {
      Object.keys(product.commonSpecs).forEach(category => {
        if (!parametersByCategory[category]) {
          parametersByCategory[category] = new Set();
        }
        
        const categorySpecs = product.commonSpecs[category];
        if (typeof categorySpecs === 'object' && categorySpecs !== null) {
          Object.keys(categorySpecs).forEach(param => {
            allParameters.add(param);
            parametersByCategory[category].add(param);
            
            // 记录参数值
            if (!parameterValues[param]) {
              parameterValues[param] = [];
            }
            parameterValues[param].push({
              productName: product.name,
              value: categorySpecs[param],
              category: category
            });
          });
        }
      });
    }
    
    // 分析 configurations 中的 specs
    if (product.configurations) {
      product.configurations.forEach(config => {
        if (config.specs) {
          Object.keys(config.specs).forEach(category => {
            if (!parametersByCategory[category]) {
              parametersByCategory[category] = new Set();
            }
            
            const categorySpecs = config.specs[category];
            if (typeof categorySpecs === 'object' && categorySpecs !== null) {
              Object.keys(categorySpecs).forEach(param => {
                allParameters.add(param);
                parametersByCategory[category].add(param);
                
                // 记录参数值（配置级别）
                if (!parameterValues[param]) {
                  parameterValues[param] = [];
                }
                parameterValues[param].push({
                  productName: `${product.name} (${config.name})`,
                  value: categorySpecs[param],
                  category: category
                });
              });
            }
          });
        }
      });
    }
  });
  
  // 3. 转换 Set 为 Array 并排序
  const finalParametersByCategory = {};
  Object.keys(parametersByCategory).forEach(category => {
    finalParametersByCategory[category] = Array.from(parametersByCategory[category]).sort();
  });
  
  // 4. 分析参数分类的优先级（基于参数数量和重要性）
  const categoryPriority = Object.keys(finalParametersByCategory)
    .map(category => ({
      category: category,
      parameterCount: finalParametersByCategory[category].length
    }))
    .sort((a, b) => b.parameterCount - a.parameterCount);
  
  // 5. 分析参数覆盖情况
  const parameterCoverage = {};
  Array.from(allParameters).forEach(param => {
    const productCount = new Set(parameterValues[param].map(v => v.productName.split(' (')[0])).size;
    parameterCoverage[param] = {
      totalProducts: formattedProducts.length,
      coveredProducts: productCount,
      coverage: (productCount / formattedProducts.length * 100).toFixed(1) + '%'
    };
  });
  
  const result = {
    productTypes: productTypes,
    primaryProductType: primaryProductType,
    isMixedTypes: isMixedTypes,
    totalParameters: allParameters.size,
    parametersByCategory: finalParametersByCategory,
    parameterValues: parameterValues,
    parameterCoverage: parameterCoverage,
    categoryPriority: categoryPriority,
    extractedCategories: Object.keys(finalParametersByCategory).sort()
  };
  
  console.log(`✅ 参数提取完成: 发现 ${result.totalParameters} 个参数，分布在 ${result.extractedCategories.length} 个类别中`);

  return result;
}

/**
 * 基于参数字段提取生成结构化的智能对比报告 (V4版本)
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Promise<Object>} 结构化的对比分析报告
 */
async function generateStructuredComparisonReportV4(formattedProducts, parameterAnalysis) {
  const systemPrompt = `你是一个资深的产品评测专家，擅长进行深入的产品对比分析。

请根据提供的产品信息，生成一份专业、全面的产品对比报告，并以严格的JSON格式返回。

分析要求：
1. **全面性要求**：必须覆盖产品数据中的所有重要技术参数，不能遗漏任何关键信息
2. **技术规格分析**：仔细分析产品数据中的每一个技术参数
3. **分类原则**：根据产品实际参数数据，按照逻辑相关性进行合理分类，确保每个重要参数都被包含
4. **分析深度**：analysis字段要详细且专业，包含各产品的具体参数值、参数差异说明、对实际使用体验的影响分析
5. **实用性**：优缺点分析要具体且实用，基于实际的技术参数差异
6. **场景化**：使用场景推荐要具体到实际应用，基于产品的实际特性
7. **购买指导**：购买建议要包含注意事项等实用信息,不要添加和价格,购买时机等相关的建议
8. **客观性**：客观公正，严格基于提供的产品数据进行分析
9. **可读性**：语言通俗易懂，但要专业准确

JSON格式要求：
{
  "technicalSpecs": [
    {
      "category": "根据实际产品参数确定的技术类别名称",
      "items": [
        {
          "name": "具体的技术参数名称",
          "productValues": {"产品A":产品A在这个技术参数名称上参数的总结,"产品B":产品B在这个技术参数名称上参数的总结,......},
          "analysis": "AI对该技术参数的专业分析和总结,一定是站在消费者的角度进行分析,包括参数差异说明、对实际使用体验的影响分析、哪些参数更优以及原因、适用场景等。不要重复产品的具体参数值，专注于分析和比较。"
        }
      ]
    }
  ],
  "prosAndCons": [
    {
      "productName": "产品名称",
      "pros": ["基于实际参数的具体优点1", "具体优点2", "具体优点3", "具体优点4", "具体优点5", "具体优点6"],
      "cons": ["基于实际参数的具体缺点1", "具体缺点2", "具体缺点3", "具体缺点4"],
      "overallRating": "详细的综合评价，要基于实际技术参数说明适合什么用户群体"
    }
  ],
  "usageScenarios": [
    {
      "scenario": "基于产品实际特性的具体使用场景名称",
      "description": "详细的场景描述，说明具体的使用需求",
      "recommendedProduct": "推荐产品名称",
      "reason": "详细的推荐理由，要基于产品的实际技术特性说明为什么适合这个场景"
    }
  ],
  "purchaseAdvice": {
    "specificAdvice": [
      {
        "userType": "具体用户类型",
        "recommendation": "详细的针对性建议,要基于产品的实际特性"
      }
    ],
    "importantNotes": ["基于产品实际特性的重要注意事项1", "重要注意事项2", "重要注意事项3"]
  }
}`;

  // 使用详细的参数分析结果中的 parametersByCategory
  const parameterInfo = Object.keys(parameterAnalysis.parametersByCategory).map(category => {
    const parameters = parameterAnalysis.parametersByCategory[category];
    return `**${category}**：${parameters.join('、')}`;
  }).join('\n');

  const userPrompt = `请基于以下从数据库获取的产品信息生成结构化的对比分析报告：

产品数据：
${JSON.stringify(formattedProducts, null, 2)}

检测到的参数字段（按分类）：
${parameterInfo}

**重要约束 - 产品名称使用规范**：
在生成JSON响应时，productValues对象中的产品名称键（key）必须严格使用以下名称，不得有任何修改：
${formattedProducts.map((product, index) => `${index + 1}. "${product.name}"`).join('\n')}

在prosAndCons数组中的productName字段也必须严格使用上述名称。
在usageScenarios数组中的recommendedProduct字段也必须严格使用上述名称。

重要提醒：
1. 请仔细检查产品数据中的每一个字段和参数，确保在technicalSpecs中全面覆盖
2. 对于每个产品数据中存在的参数，都应该在对应的技术类别中进行分析
3. 特别关注上述检测到的参数字段，确保这些字段都被包含在分析中
4. 如果某个参数在不同产品中的表达方式不同，请统一进行对比分析
5. 确保生成的JSON格式严格符合要求，特别是technicalSpecs部分要全面且详细
`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);

  try {
    // 尝试解析JSON响应
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应中未找到有效的JSON格式');
    }

    const parsedReport = JSON.parse(jsonMatch[0]);
    return parsedReport;
  } catch (parseError) {
    console.error('❌ JSON解析失败:', parseError.message);
    console.log('原始AI响应:', response);

    // 如果JSON解析失败，返回一个简化的备用格式
    return {
      summary: {
        title: "产品对比报告",
        productCount: formattedProducts.length,
        category: "未知类别",
        keyDifferences: ["AI分析格式解析失败"]
      },
      technicalSpecs: [],
      prosAndCons: [],
      usageScenarios: [],
      purchaseAdvice: {
        budgetConsiderations: "请查看原始分析文本",
        generalRecommendation: "AI分析格式解析失败，请联系技术支持",
        specificAdvice: []
      },
      rawAnalysis: response, // 保留原始分析文本作为备用
      parseError: parseError.message
    };
  }
}

/**
 * 根据产品名称列表获取产品参数对比数据（V4版本 - 基于参数字段提取的智能对比）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesV4 = async (productNames) => {
  try {
    console.log('🔍 开始产品对比 V4 - 基于参数字段提取的智能分析');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 从 NewProduct 数据库中查找产品
    const findResult = await findProductsByNamesV4(productNames);

    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 检查缓存
    console.log('🔍 检查缓存中是否存在对比结果...');
    const cachedResult = await ProductComparisonV4Cache.findByProductNames(productNames);
    
    if (cachedResult) {
      console.log('✅ 找到缓存结果，直接返回');
      return cachedResult.getCachedResult();
    }
    
    console.log('❌ 未找到缓存，开始AI分析...');

    // 4. 格式化产品数据用于 AI 分析
    const formattedProductData = products.map(product => formatProductForAI(product));

    // 5. 提取产品参数字段和类型信息
    console.log('🔍 提取产品参数字段和类型信息...');
    const parameterAnalysis = extractProductParametersAndTypes(formattedProductData);

    // 6. 使用 AI 进行产品对比分析（基于明确的参数字段）
    console.log('🤖 调用 AI 进行基于参数字段的智能产品对比分析...');
    const structuredReport = await generateStructuredComparisonReportV4(formattedProductData, parameterAnalysis);

    if (!structuredReport) {
      return {
        success: false,
        error: 'AI 分析失败：未能生成对比报告',
        data: null
      };
    }

    // 7. 分析产品类别信息（由于已在findProductsByNamesV4中确保同类型，这里必然是同类型）
    const productTypes = [...new Set(products.map(p => p.productType))];
    const productCategory = productTypes[0]; // 必然只有一个类型
    const isSameCategory = true; // 必然是同类型
    const crossCategoryNote = null; // 不再支持跨类别对比

    // 8. 构建最终返回结果
    const result = {
      success: true,
      data: {
        // 产品基本信息（仅包含skuName和imageUrl）
        products: products.map(product => ({
          skuName: product.skuName,
          imageUrl: product.imageUrl
        })),

        // AI 结构化对比分析结果
        aiAnalysis: {
          productCategory: productCategory,
          isSameCategory: isSameCategory,
          crossCategoryNote: crossCategoryNote,
          structuredReport: structuredReport, // 基于参数字段的结构化JSON报告
          aiModel: DEEPSEEK_MODEL,
        },
        
        // 添加默认的评分统计信息（确保数据结构一致性）
        ratingStats: {
          averageRating: 0,
          totalRatings: 0,
          lastRatingUpdate: null
        }
      }
    };
    
    // 9. 保存到缓存
    let comparisonCacheId = null;
    try {
      console.log('💾 保存对比结果到缓存...');
      const savedCache = await ProductComparisonV4Cache.createCache(productNames, result);
      comparisonCacheId = savedCache._id;
      console.log('✅ 缓存保存成功，ID:', comparisonCacheId);
    } catch (cacheError) {
      console.error('⚠️ 缓存保存失败:', cacheError.message);
      // 缓存失败不影响主要功能，继续返回结果
    }

    // 10. 将缓存ID添加到返回结果中
    if (comparisonCacheId) {
      result.data.comparisonCacheId = comparisonCacheId;
    }

    console.log('✅ 产品对比 V4 完成');
    return result;

  } catch (error) {
    console.error('❌ 产品对比 V4 失败:', error);
    return {
      success: false,
      error: `产品对比失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 从 NewProduct 数据库中查找产品（V4版本）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 查找结果
 */
const findProductsByNamesV4 = async (productNames) => {
  try {
    const products = [];
    const notFoundProducts = [];

    for (const productName of productNames) {
      console.log(`🔍 搜索产品: ${productName}`);

      // 使用智能搜索匹配产品
      const product = await findSingleProductByName(productName);

      if (product) {
        products.push(product);
        console.log(`✅ 找到产品: ${product.skuName}`);
      } else {
        notFoundProducts.push(productName);
        console.log(`❌ 未找到产品: ${productName}`);
      }
    }

    if (products.length < 2) {
      return {
        success: false,
        error: `找到的产品数量不足，无法进行对比。找到 ${products.length} 个，需要至少 2 个产品`,
        data: null
      };
    }

    // 检查产品类型是否一致（强制要求，不支持跨类别对比）
    const productTypes = [...new Set(products.map(p => p.productType))];

    if (productTypes.length > 1) {
      const errorMessage = `不支持不同类型的产品对比。检测到的产品类型: ${productTypes.join(', ')}，请选择相同类型的产品进行对比`;
      console.log(`❌ ${errorMessage}`);
      return {
        success: false,
        error: errorMessage,
        data: null
      };
    }

    return {
      success: true,
      data: {
        products,
        notFoundProducts,
        categoryWarning: null
      }
    };

  } catch (error) {
    console.error('查找产品失败:', error);
    return {
      success: false,
      error: `查找产品失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 根据产品名称查找单个产品（精确匹配 skuName）
 * @param {String} productName 产品名称
 * @returns {Promise<Object|null>} 产品对象或null
 */
const findSingleProductByName = async (productName) => {
  try {
    // 直接通过 skuName 精确匹配
    const product = await NewProduct.findOne({
      skuName: productName.trim()
    });

    return product;

  } catch (error) {
    console.error(`查找单个产品失败 (${productName}):`, error);
    return null;
  }
};

/**
 * 格式化产品数据用于 AI 分析（V4版本，增强参数信息）
 * @param {Object} product NewProduct 对象
 * @returns {Object} 格式化后的产品数据
 */
const formatProductForAI = (product) => {
  // 定义要排除的字段类别
  const EXCLUDED_CATEGORIES = ['基本信息',"爬取信息","接口其他"];

  // 过滤 commonSpecs，排除指定的字段类别
  const filteredCommonSpecs = {};
  if (product.commonSpecs) {
    Object.keys(product.commonSpecs).forEach(category => {
      if (!EXCLUDED_CATEGORIES.includes(category)) {
        filteredCommonSpecs[category] = product.commonSpecs[category];
      }
    });
  }

  return {
    // 基本信息
    name: product.skuName,
    productType: product.productType,
    imageurl: product.imageUrl,

    // 配置信息
    configurations: product.configurations || [],

    // 规格参数（已过滤排除的字段类别）
    commonSpecs: filteredCommonSpecs,
  };
};

module.exports = {
  compareProductsByNamesV4,
  findProductsByNamesV4,
  formatProductForAI
};
