# 选选小程序 - 用户注册和登录流程详细文档

## 🎯 流程概览

选选小程序采用**统一认证**的设计理念，用户只需要获取一次验证码即可完成登录或注册操作，提供简洁流畅的用户体验。

### 核心特性
- ✅ **统一认证**：一次验证码同时支持登录和注册
- ✅ **防误操作**：弹窗关闭保护机制，支持重试
- ✅ **临时令牌**：安全的新用户注册流程
- ✅ **微信登录**：支持微信快捷登录

---

## 📋 详细流程步骤

### 第一阶段：获取验证码

#### 用户操作
1. 用户输入手机号
2. 点击"获取验证码"按钮

#### 前端处理
**函数：** `sendVerifyCode()`
```javascript
// 验证手机号格式
if (!util.isValidPhone(phone)) {
  util.showToast('请输入正确的手机号');
  return;
}

// 调用接口
auth.sendVerifyCode(phone, 'auth')
```

#### 后端接口
- **接口：** `POST /auth/send-verify-code`
- **函数：** `sendVerifyCode()`
- **参数：** `{ phone: "13800138000", type: "auth" }`

#### 数据库变化
**表：** `verify_codes`
```sql
INSERT INTO verify_codes (
  phone, 
  code, 
  type, 
  created_at, 
  expires_at, 
  used
) VALUES (
  '13800138000',
  '123456',
  'auth',
  NOW(),
  DATE_ADD(NOW(), INTERVAL 5 MINUTE),
  false
);
```

#### 前端状态变化
```javascript
{
  phone: "13800138000",
  countdownDisabled: true,          // 开始60秒倒计时
  countdownText: "59秒后重新获取",
  canRetryRegister: false,          // 重置重试状态
  tempAuthData: null               // 清空旧临时数据
}
```

---

### 第二阶段：统一认证

#### 用户操作
1. 用户输入接收到的验证码
2. 点击"登录 / 注册"按钮

#### 前端处理
**函数：** `phoneAuth()`

**重试机制检查：**
```javascript
// 如果用户之前关闭了弹窗，支持直接重试
if (canRetryRegister && tempAuthData) {
  this.setData({
    showNicknameModal: true,
    nickname: '',
    isNicknameValid: false
  });
  return;
}
```

**正常认证流程：**
```javascript
// 验证输入
if (!isPhoneValid || !isCodeValid) {
  // 显示错误提示
  return;
}

// 调用认证接口
auth.phoneAuth(phone, verifyCode)
```

#### 后端接口
- **接口：** `POST /auth/phone-auth`
- **函数：** `phoneAuth()`
- **参数：** `{ phone: "13800138000", verifyCode: "123456" }`

#### 后端处理逻辑
```javascript
async function phoneAuth(phone, verifyCode) {
  // 1. 验证验证码
  const codeRecord = await VerifyCode.findOne({
    phone,
    code: verifyCode,
    type: 'auth',
    used: false,
    expires_at: { $gt: new Date() }
  });
  
  if (!codeRecord) {
    throw new Error('验证码无效或已过期');
  }
  
  // 2. 标记验证码已使用
  await VerifyCode.updateOne(
    { _id: codeRecord._id },
    { used: true }
  );
  
  // 3. 查询用户是否存在
  const existingUser = await User.findOne({ phone });
  
  if (existingUser) {
    // 老用户 - 直接登录
    const token = jwt.sign({ userId: existingUser._id }, JWT_SECRET);
    return {
      isNewUser: false,
      user: existingUser,
      token
    };
  } else {
    // 新用户 - 生成临时令牌
    const tempToken = `temp_${uuidv4()}`;
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟
    
    await TempAuthToken.create({
      phone,
      token: tempToken,
      expires_at: expiresAt
    });
    
    return {
      isNewUser: true,
      phone,
      tempToken,
      expires_at: expiresAt
    };
  }
}
```

#### 数据库变化

**老用户情况：**
```sql
-- 标记验证码已使用
UPDATE verify_codes 
SET used = true 
WHERE phone = '13800138000' AND code = '123456';
```

**新用户情况：**
```sql
-- 标记验证码已使用
UPDATE verify_codes 
SET used = true 
WHERE phone = '13800138000' AND code = '123456';

-- 创建临时令牌
INSERT INTO temp_auth_tokens (
  phone,
  token,
  created_at,
  expires_at,
  used
) VALUES (
  '13800138000',
  'temp_1a2b3c4d5e6f7g8h9i',
  NOW(),
  DATE_ADD(NOW(), INTERVAL 5 MINUTE),
  false
);
```

#### 前端状态变化

**🔵 老用户登录成功：**
```javascript
{
  // 页面状态保持默认
  showNicknameModal: false,
  tempAuthData: null,
  canRetryRegister: false
}

// 全局状态更新
app.globalData = {
  isLoggedIn: true,
  userInfo: userData,
  token: jwtToken
}

// 跳转首页
wx.switchTab({ url: '/pages/index/index' });
```

**🟢 新用户显示注册弹窗：**
```javascript
{
  showNicknameModal: true,          // 显示昵称设置弹窗
  tempAuthData: {                   // 保存临时认证数据
    phone: "13800138000",
    tempToken: "temp_1a2b3c4d5e6f7g8h9i",
    expires_at: "2024-01-01T12:05:00Z"
  },
  canRetryRegister: true,           // 允许重试注册
  nickname: "",                     // 重置昵称输入
  isNicknameValid: false
}
```

---

### 第三阶段：完成注册（仅新用户）

#### 用户操作
1. 用户在弹窗中输入昵称（2-20个字符）
2. 点击"完成注册"按钮

#### 前端处理
**函数：** `completeRegister()`
```javascript
// 验证昵称
if (!isNicknameValid) {
  util.showToast('请输入2-20个字符的用户名');
  return;
}

// 检查临时认证数据
if (!tempAuthData || !tempAuthData.tempToken) {
  util.showToast('认证数据丢失，请重新获取验证码');
  this.resetToInitialState();
  return;
}

// 调用完成注册接口
auth.completeRegister({
  phone: tempAuthData.phone,
  tempToken: tempAuthData.tempToken,
  nickname: nickname
})
```

#### 后端接口
- **接口：** `POST /auth/complete-register`
- **函数：** `completeRegister()`
- **参数：** `{ phone: "13800138000", tempToken: "temp_xxx", nickname: "张三" }`

#### 后端处理逻辑
```javascript
async function completeRegister(phone, tempToken, nickname) {
  // 1. 验证临时令牌
  const tokenRecord = await TempAuthToken.findOne({
    phone,
    token: tempToken,
    used: false,
    expires_at: { $gt: new Date() }
  });
  
  if (!tokenRecord) {
    throw new Error('认证令牌无效或已过期');
  }
  
  // 2. 创建新用户
  const newUser = await User.create({
    phone,
    nickname,
    avatar: '/assets/images/default-avatar.png',
    created_at: new Date(),
    updated_at: new Date()
  });
  
  // 3. 生成正式JWT令牌
  const token = jwt.sign({ userId: newUser._id }, JWT_SECRET);
  
  // 4. 清理临时令牌
  await TempAuthToken.deleteOne({ _id: tokenRecord._id });
  
  return {
    user: newUser,
    token
  };
}
```

#### 数据库变化
```sql
-- 创建新用户
INSERT INTO users (
  phone,
  nickname,
  avatar,
  created_at,
  updated_at
) VALUES (
  '13800138000',
  '张三',
  '/assets/images/default-avatar.png',
  NOW(),
  NOW()
);

-- 删除临时令牌
DELETE FROM temp_auth_tokens 
WHERE phone = '13800138000' AND token = 'temp_1a2b3c4d5e6f7g8h9i';
```

#### 前端状态变化
```javascript
{
  showNicknameModal: false,         // 关闭弹窗
  showCloseConfirm: false,
  tempAuthData: null,               // 清空临时数据
  canRetryRegister: false,          // 重置重试状态
  nickname: "",
  isNicknameValid: false
}

// 全局状态更新
app.globalData = {
  isLoggedIn: true,
  userInfo: newUserData,
  token: jwtToken
}

// 跳转首页
wx.switchTab({ url: '/pages/index/index' });
```

---

## 🔐 验证码和临时令牌机制详解

### 验证码 (Verify Code)

#### 数据结构
```javascript
{
  id: 1,
  phone: "13800138000",
  code: "123456",
  type: "auth",                     // 统一认证类型
  created_at: "2024-01-01T12:00:00Z",
  expires_at: "2024-01-01T12:05:00Z", // 5分钟过期
  used: false                       // 是否已使用（一次性）
}
```

#### 生命周期
1. **生成** → 调用短信服务发送 → 存储到数据库
2. **验证** → phoneAuth接口验证 → 标记为已使用
3. **清理** → 定时任务清理过期记录

#### 安全特性
- ✅ **5分钟过期**：防止验证码被长期滥用
- ✅ **一次性使用**：验证后立即标记已使用
- ✅ **类型标识**：支持不同业务场景的验证码

### 临时令牌 (Temporary Auth Token)

#### 数据结构
```javascript
{
  id: 1,
  phone: "13800138000",
  token: "temp_1a2b3c4d5e6f7g8h9i",
  created_at: "2024-01-01T12:00:00Z",
  expires_at: "2024-01-01T12:05:00Z", // 5分钟过期
  used: false
}
```

#### 生命周期
1. **生成** → phoneAuth确认新用户后创建
2. **使用** → completeRegister接口验证并使用
3. **销毁** → 注册完成后立即删除

#### 安全特性
- ✅ **短期有效**：5分钟内必须完成注册
- ✅ **一次性使用**：注册完成后立即销毁
- ✅ **随机生成**：使用UUID确保唯一性

---

## 🔄 特殊情况处理

### 1. 用户误操作关闭弹窗

#### 问题场景
用户在设置昵称时意外点击关闭按钮或触摸空白区域

#### 解决方案
- **防误操作**：移除点击空白区域关闭功能
- **确认机制**：点击关闭按钮显示确认弹窗
- **重试支持**：保留临时数据，支持重新打开弹窗

#### 前端处理流程
```javascript
// 1. 用户点击关闭按钮
tryCloseNicknameModal() {
  this.setData({ showCloseConfirm: true });
}

// 2. 用户确认关闭
confirmCloseModal() {
  this.setData({
    showNicknameModal: false,
    showCloseConfirm: false
    // 注意：tempAuthData 和 canRetryRegister 保持不变
  });
  util.showToast('可点击登录/注册按钮重新设置用户名', 'none', 3000);
}

// 3. 用户重新点击"继续注册"
phoneAuth() {
  if (canRetryRegister && tempAuthData) {
    // 直接重新打开弹窗，无需重新验证
    this.setData({ showNicknameModal: true });
    return;
  }
  // ... 正常认证流程
}
```

#### 按钮文字变化
- 初始状态：**"登录 / 注册"**
- 重试状态：**"继续注册"**

### 2. 临时令牌过期

#### 问题场景
用户获取验证码后，超过5分钟才完成昵称设置

#### 解决方案
- **友好提示**：显示专门的过期提示弹窗
- **引导重试**：指导用户重新获取验证码
- **状态重置**：清理所有相关状态

#### 前端处理流程
```javascript
completeRegister() {
  auth.completeRegister(...)
    .catch(err => {
      if (err.message && err.message.includes('过期')) {
        util.showModal({
          title: '认证已过期',
          content: '请重新获取验证码完成注册',
          showCancel: false,
          confirmText: '知道了',
          success: () => {
            this.resetToInitialState();
          }
        });
      } else {
        util.showToast(err.message || '注册失败，请稍后再试');
      }
    });
}
```

### 3. 重新获取验证码

#### 问题场景
用户需要重新获取验证码（验证码过期、接收失败等）

#### 解决方案
- **状态重置**：清空旧的临时数据和重试状态
- **重新开始**：从第一阶段重新开始流程

#### 前端处理流程
```javascript
sendVerifyCode() {
  // 重置注册相关状态（新验证码可以重新尝试）
  this.setData({
    canRetryRegister: false,
    tempAuthData: null
  });
  
  // ... 发送验证码逻辑
}
```

---

## 🖥️ 后端接口汇总

| 前端函数 | HTTP接口 | 后端函数 | 主要功能 | 参数 |
|---------|---------|---------|---------|------|
| `sendVerifyCode()` | `POST /auth/send-verify-code` | `sendVerifyCode` | 发送验证码 | `{phone, type}` |
| `phoneAuth()` | `POST /auth/phone-auth` | `phoneAuth` | 统一认证 | `{phone, verifyCode}` |
| `completeRegister()` | `POST /auth/complete-register` | `completeRegister` | 完成注册 | `{phone, tempToken, nickname}` |
| `wxLogin()` | `POST /auth/wx-login` | `wxLogin` | 微信登录 | `{code}` |

---

## 📊 数据库表结构

### verify_codes 表
```sql
CREATE TABLE verify_codes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(11) NOT NULL,
  code VARCHAR(6) NOT NULL,
  type VARCHAR(20) NOT NULL,           -- 'auth', 'reset', etc.
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  INDEX idx_phone_type (phone, type),
  INDEX idx_expires_at (expires_at)
);
```

### temp_auth_tokens 表
```sql
CREATE TABLE temp_auth_tokens (
  id INT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(11) NOT NULL,
  token VARCHAR(100) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  INDEX idx_phone (phone),
  INDEX idx_token (token),
  INDEX idx_expires_at (expires_at)
);
```

### users 表
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(11) NOT NULL UNIQUE,
  nickname VARCHAR(50) NOT NULL,
  avatar VARCHAR(255) DEFAULT '/assets/images/default-avatar.png',
  wx_openid VARCHAR(100) NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_phone (phone),
  INDEX idx_wx_openid (wx_openid)
);
```

---

## 🔗 微信登录流程

### 快速登录
- **接口：** `POST /auth/wx-login`
- **参数：** `{ code }` (微信授权码)
- **处理：** 通过微信接口获取openid，查询或创建用户
- **返回：** 用户信息和JWT令牌

### 新用户处理
- 如果是微信新用户，跳转到单独的完善信息页面
- 与手机号注册流程分离，避免混淆

---

## 📈 流程图总结

```
[输入手机号] → [获取验证码] → [输入验证码] → [点击登录/注册]
                                                    ↓
                                               [认证接口]
                                                    ↓
                                              [用户存在？]
                                               ↙        ↘
                                        [是：直接登录]  [否：生成临时令牌]
                                              ↓              ↓
                                        [跳转首页]    [显示昵称弹窗]
                                                           ↓
                                                    [用户设置昵称]
                                                           ↓
                                                    [完成注册接口]
                                                           ↓
                                                    [创建用户账号]
                                                           ↓
                                                     [跳转首页]
```

---

## ✅ 设计优势

1. **用户体验**：一次验证码解决所有问题，流程简洁
2. **安全性**：临时令牌机制，防止恶意注册
3. **容错性**：支持重试，防止误操作导致流程中断
4. **可维护性**：清晰的状态管理和错误处理
5. **扩展性**：支持多种登录方式，便于后续扩展

---

*本文档记录了选选小程序完整的用户认证流程，包括所有技术细节和特殊情况处理方案。*
