<!-- AI推荐弹窗 -->
<view class="ai-recommend-modal {{show ? 'show' : ''}}" wx:if="{{show}}">
  <view class="modal-mask" bindtap="onClose"></view>
  <view class="modal-content">
    <!-- 标题栏 -->
    <view class="modal-header">
      <text class="modal-title">AI智能推荐</text>
      <text class="close-btn" bindtap="onClose">×</text>
    </view>

    <!-- AI处理状态显示 -->
    <view class="processing-status" wx:if="{{processing}}">
      <view class="processing-animation">
        <view class="loading-spinner"></view>
      </view>
      <view class="processing-message">{{processingMessage}}</view>
      <view class="processing-tip">这通常需要5-15秒，请耐心等待...</view>
    </view>

    <!-- 表单内容 -->
    <view class="modal-body" wx:else>
      <!-- 选中的产品类别显示 -->
      <view class="form-section" wx:if="{{selectedCategory}}">
        <text class="section-title">产品类别</text>
        <view class="selected-category">
          <text class="category-text">{{selectedCategory}}</text>
        </view>
      </view>

      <!-- 品牌选择 -->
      <view class="form-section" wx:if="{{selectedCategory}}">
        <text class="section-title">品牌偏好 (可选择多个)</text>
        <view class="brand-grid">
          <view class="brand-item {{brandStatus[brand] ? 'selected' : ''}}" 
                wx:for="{{availableBrands}}" wx:key="*this" wx:for-item="brand"
                bindtap="onToggleBrand" data-brand="{{brand}}">
            <text class="brand-text">{{brand}}</text>
            <text class="brand-check" wx:if="{{brandStatus[brand]}}">✓</text>
          </view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="tip-section" wx:if="{{selectedCategory}}">
        <text class="tip-icon">💡</text>
        <text class="tip-text">AI将根据您的问题描述和品牌偏好，为您推荐2-5个最适合的产品选项</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="modal-footer" wx:if="{{!processing}}">
      <button class="cancel-btn" bindtap="onClose">取消</button>
      <button class="confirm-btn {{canConfirm && !loading ? 'active' : 'disabled'}}" 
              bindtap="onConfirm" disabled="{{!canConfirm || loading}}">
        <text wx:if="{{loading}}">分析中...</text>
        <text wx:else>获取AI推荐</text>
      </button>
    </view>

    <!-- 处理中的底部按钮 -->
    <view class="modal-footer" wx:if="{{processing}}">
      <button class="cancel-btn" bindtap="onClose">取消</button>
      <button class="confirm-btn disabled" disabled="true">
        处理中...
      </button>
    </view>
  </view>
</view>