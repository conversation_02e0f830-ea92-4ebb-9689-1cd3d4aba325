# 环境变量配置说明

## 基本配置

在你的服务器项目根目录创建 `.env` 文件，添加以下配置：

```env
# 服务器配置
PORT=3000
NODE_ENV=production
BASE_URL=https://your-domain.com

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/xuanxuan

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRE=7d

# 存储配置 - 改为 'oss' 启用OSS存储
STORAGE_TYPE=oss

# 阿里云OSS配置（必填）
OSS_ACCESS_KEY_ID=LTAI5txxxxxxxxxxxx
OSS_ACCESS_KEY_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxx
OSS_BUCKET=xuanxuan-files
OSS_REGION=oss-cn-hangzhou
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_DOMAIN=https://xuanxuan-files.oss-cn-hangzhou.aliyuncs.com
OSS_FOLDER=xuanxuan-app

# 微信小程序配置
WECHAT_APP_ID=wx1234567890abcdef
WECHAT_APP_SECRET=1234567890abcdef1234567890abcdef
```

## 配置项说明

### 存储配置
- `STORAGE_TYPE`: 存储类型，设置为 `local` 使用本地存储，设置为 `oss` 使用阿里云OSS存储

### OSS配置项
- `OSS_ACCESS_KEY_ID`: 你的阿里云AccessKey ID
- `OSS_ACCESS_KEY_SECRET`: 你的阿里云AccessKey Secret
- `OSS_BUCKET`: 你创建的OSS Bucket名称
- `OSS_REGION`: OSS地域，例如 `oss-cn-hangzhou`
- `OSS_ENDPOINT`: OSS接入点，例如 `https://oss-cn-hangzhou.aliyuncs.com`
- `OSS_DOMAIN`: OSS访问域名，例如 `https://你的bucket名称.oss-cn-hangzhou.aliyuncs.com`
- `OSS_FOLDER`: 文件在OSS中的文件夹前缀，例如 `xuanxuan-app`

## 如何切换存储方式

### 从本地存储切换到OSS存储
1. 确保已经配置好OSS相关环境变量
2. 将 `STORAGE_TYPE` 设置为 `oss`
3. 重启你的Node.js服务器

### 从OSS存储切换回本地存储
1. 将 `STORAGE_TYPE` 设置为 `local`
2. 重启你的Node.js服务器

## 注意事项

1. **安全性**: 不要将包含真实AccessKey的 `.env` 文件提交到代码仓库
2. **权限**: 确保OSS Bucket设置为"公共读"权限，否则图片无法正常访问
3. **跨域**: 确保OSS Bucket配置了正确的跨域规则
4. **费用**: 使用OSS会产生存储和流量费用，请关注阿里云账单 