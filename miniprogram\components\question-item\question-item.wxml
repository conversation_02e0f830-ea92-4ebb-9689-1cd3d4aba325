<!-- 问题组件 -->
<view class="question-item" bindtap="onQuestionTap">
  <!-- 用户信息 -->
  <view class="user-info">
    <image class="avatar" src="{{question.user.avatar || '/assets/images/default-avatar.png'}}"></image>
    <view class="info">
      <text class="nickname">{{question.user.nickname}}</text>
      <text class="time">{{question.createdAt}}</text>
    </view>
    <!-- 问题状态标签 -->
    <view class="status-tag {{question.status === 'closed' ? 'status-closed' : 'status-open'}}">
      {{question.status === 'closed' ? '已关闭' : '进行中'}}
    </view>
  </view>
  
  <!-- 问题内容 -->
  <view class="question-content">
    <view class="title">
      {{question.title}}
      <view class="tag-required" wx:if="{{question.requireReason}}">必填理由</view>
    </view>
    <view class="content" wx:if="{{question.scene}}">{{question.scene}}</view>
    
    <!-- 问题图片 -->
    <view class="image-list" wx:if="{{question.images && question.images.length > 0}}">
      <image 
        class="question-image {{question.images.length === 1 ? 'single' : ''}}" 
        wx:for="{{question.images}}" 
        wx:for-item="img" 
        wx:key="*this" 
        src="{{img}}" 
        mode="{{question.images.length === 1 ? 'widthFix' : 'aspectFill'}}"
        catchtap="previewImage"
        data-urls="{{question.images}}"
        data-current="{{img}}"
      ></image>
    </view>
    
    <!-- 选项列表 -->
    <view class="options-list" wx:if="{{question.options && question.options.length > 0}}">
      <view class="option-item" wx:for="{{question.options}}" wx:for-item="option" wx:key="id">
        <text>{{option.content}}</text>
        <text class="vote-count">{{option.voteCount || 0}}票</text>
      </view>
    </view>
  </view>
  
  <!-- 问题数据 -->
  <view class="question-stats">
    <view class="stat-item">
      <text class="iconfont icon-comment"></text>
      <text>{{question.commentCount || 0}}</text>
    </view>
    <view class="stat-item">
      <text class="iconfont icon-vote"></text>
      <text>{{question.totalVotes || 0}}</text>
    </view>
    <view class="stat-item">
      <text class="iconfont icon-share"></text>
      <text></text>
    </view>
    <!-- 查看结果按钮 - 仅对已关闭的问题显示 -->
    <view wx:if="{{question.status === 'closed'}}" class="view-result-btn" catchtap="viewResult">
      <text class="iconfont icon-data-analysis"></text>
      <text>查看结果</text>
    </view>
  </view>
</view> 