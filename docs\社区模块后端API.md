# 社区模块后端API设计

## 基础信息
- 基础路径: `/api/v1/community`
- 所有API需要认证，除非特别说明
- 默认返回格式为JSON
- 所有返回包含状态码和消息:
  ```json
  {
    "success": true/false,
    "code": 200/400/401/403/404/500,
    "message": "操作成功/失败的消息",
    "data": {} // 返回的数据
  }
  ```

## 1. 问题相关API

### 1.1 创建问题
- **路径**: `POST /api/v1/community/questions`
- **描述**: 创建新问题
- **请求体**:
  ```json
  {
    "title": "问题标题",
    "background": "问题背景",
    "content": "问题主干",
    "options": [
      {"content": "选项1内容"},
      {"content": "选项2内容"}
    ],
    "isAnonymous": true/false,
    "requireReason": true/false,
    "visibility": {
      "type": "public"/"filtered",
      "filters": {
        "gender": ["male", "female"],
        "minAge": 18,
        "maxAge": 35,
        "regions": ["北京", "上海"],
        "occupations": ["学生", "工程师"]
      }
    },
    "expiryTime": "2023-12-31T23:59:59Z"
  }
  ```
- **返回**:
  ```json
  {
    "success": true,
    "code": 201,
    "message": "问题创建成功",
    "data": {
      "id": "问题ID",
      "title": "问题标题",
      "createdAt": "创建时间"
    }
  }
  ```

### 1.2 获取问题列表
- **路径**: `GET /api/v1/community/questions`
- **描述**: 获取问题列表，支持分页和筛选
- **参数**:
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认10
  - `status`: 问题状态 "open"/"closed"，可选
  - `userId`: 用户ID，获取特定用户的问题，可选
  - `sortBy`: 排序字段 "newest"/"hottest"/"expiringSoon"，默认"newest"
- **返回**:
  ```json
  {
    "success": true,
    "code": 200,
    "data": {
      "questions": [
        {
          "id": "问题ID",
          "title": "问题标题",
          "options": [{"id": "选项ID", "content": "选项内容"}],
          "totalVotes": 100,
          "commentCount": 50,
          "createdAt": "创建时间",
          "expiryTime": "截止时间",
          "status": "open/closed",
          "user": {
            "id": "用户ID",
            "nickname": "用户昵称",
            "avatar": "头像URL"
          },
          "isAnonymous": true/false
        }
      ],
      "pagination": {
        "total": 100,
        "page": 1,
        "limit": 10,
        "pages": 10
      }
    }
  }
  ```

### 1.3 获取问题详情
- **路径**: `GET /api/v1/community/questions/:id`
- **描述**: 获取问题详情
- **返回**:
  ```json
  {
    "success": true,
    "code": 200,
    "data": {
      "id": "问题ID",
      "title": "问题标题",
      "background": "问题背景",
      "content": "问题主干",
      "options": [
        {
          "id": "选项ID",
          "content": "选项内容",
          "voteCount": 50,
          "percentage": 50
        }
      ],
      "isAnonymous": true/false,
      "requireReason": true/false,
      "visibility": {
        "type": "public"/"filtered",
        "filters": {}
      },
      "expiryTime": "截止时间",
      "status": "open/closed",
      "totalVotes": 100,
      "commentCount": 50,
      "createdAt": "创建时间",
      "user": {
        "id": "用户ID",
        "nickname": "用户昵称",
        "avatar": "头像URL"
      },
      "hasVoted": true/false,
      "votedOption": "已投票选项ID"
    }
  }
  ```

### 1.4 更新问题
- **路径**: `PUT /api/v1/community/questions/:id`
- **描述**: 更新问题(仅问题创建者可操作，且仅在无人回答时可修改)
- **请求体**: 同创建问题，但字段可选
- **返回**: 成功消息

### 1.5 删除问题
- **路径**: `DELETE /api/v1/community/questions/:id`
- **描述**: 删除问题(仅问题创建者可操作，且仅在无人回答时可删除)
- **返回**: 成功消息

## 2. 回答相关API

### 2.1 提交回答
- **路径**: `POST /api/v1/community/questions/:questionId/answers`
- **描述**: 提交对问题的回答(选择选项)
- **请求体**:
  ```json
  {
    "optionId": "选项ID",
    "content": "理由内容(当问题requireReason为true时必填)",
    "isAnonymous": true/false
  }
  ```
- **返回**: 成功消息和回答ID

### 2.2 获取问题的回答列表
- **路径**: `GET /api/v1/community/questions/:questionId/answers`
- **描述**: 获取问题的所有回答
- **参数**:
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认10
  - `sortBy`: 排序方式，"newest"/"mostLiked"，默认"newest"
  - `optionId`: 筛选特定选项的回答，可选
- **返回**:
  ```json
  {
    "success": true,
    "code": 200,
    "data": {
      "answers": [
        {
          "id": "回答ID",
          "optionId": "选项ID",
          "optionContent": "选项内容",
          "content": "理由内容",
          "isAnonymous": true/false,
          "likes": 10,
          "hasLiked": true/false,
          "commentCount": 5,
          "createdAt": "创建时间",
          "user": {
            "id": "用户ID",
            "nickname": "用户昵称",
            "avatar": "头像URL"
          }
        }
      ],
      "pagination": {
        "total": 100,
        "page": 1,
        "limit": 10,
        "pages": 10
      }
    }
  }
  ```

### 2.3 点赞回答
- **路径**: `POST /api/v1/community/answers/:id/like`
- **描述**: 点赞或取消点赞回答
- **请求体**:
  ```json
  {
    "action": "like"/"unlike"
  }
  ```
- **返回**: 成功消息和当前点赞数

## 3. 评论相关API

### 3.1 添加评论
- **路径**: `POST /api/v1/community/answers/:answerId/comments`
- **描述**: 对回答添加评论
- **请求体**:
  ```json
  {
    "content": "评论内容",
    "isAnonymous": true/false,
    "parentId": "父评论ID(可选，回复某条评论时使用)"
  }
  ```
- **返回**: 成功消息和评论ID

### 3.2 获取评论列表
- **路径**: `GET /api/v1/community/answers/:answerId/comments`
- **描述**: 获取回答的评论列表
- **参数**:
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认20
- **返回**:
  ```json
  {
    "success": true,
    "code": 200,
    "data": {
      "comments": [
        {
          "id": "评论ID",
          "content": "评论内容",
          "isAnonymous": true/false,
          "createdAt": "创建时间",
          "user": {
            "id": "用户ID",
            "nickname": "用户昵称",
            "avatar": "头像URL"
          },
          "replies": [
            {
              "id": "回复ID",
              "content": "回复内容",
              "isAnonymous": true/false,
              "createdAt": "创建时间",
              "user": {
                "id": "用户ID",
                "nickname": "用户昵称",
                "avatar": "头像URL"
              },
              "targetUser": {
                "id": "目标用户ID",
                "nickname": "目标用户昵称"
              }
            }
          ]
        }
      ],
      "pagination": {
        "total": 50,
        "page": 1,
        "limit": 20,
        "pages": 3
      }
    }
  }
  ```

### 3.3 删除评论
- **路径**: `DELETE /api/v1/community/comments/:id`
- **描述**: 删除评论(仅评论作者可操作)
- **返回**: 成功消息

## 4. 用户中心API

### 4.1 获取用户发布的问题
- **路径**: `GET /api/v1/community/users/me/questions`
- **描述**: 获取当前用户发布的问题
- **参数**: 同获取问题列表API
- **返回**: 同获取问题列表API

### 4.2 获取用户的回答
- **路径**: `GET /api/v1/community/users/me/answers`
- **描述**: 获取当前用户的所有回答
- **参数**:
  - `page`: 页码，默认1
  - `limit`: 每页数量，默认10
- **返回**: 包含问题信息的回答列表

### 4.3 获取用户点赞的回答
- **路径**: `GET /api/v1/community/users/me/likes`
- **描述**: 获取当前用户点赞的回答
- **参数**: 同获取用户的回答API
- **返回**: 同获取用户的回答API

## 5. 统计API

### 5.1 获取问题统计数据
- **路径**: `GET /api/v1/community/stats/questions/:id`
- **描述**: 获取问题的统计数据
- **返回**:
  ```json
  {
    "success": true,
    "code": 200,
    "data": {
      "totalVotes": 100,
      "optionStats": [
        {
          "id": "选项ID",
          "content": "选项内容",
          "voteCount": 50,
          "percentage": 50
        }
      ],
      "demographics": {
        "gender": {
          "male": 60,
          "female": 35,
          "secret": 5
        },
        "age": {
          "18-24": 30,
          "25-34": 40,
          "35-44": 20,
          "45+": 10
        },
        "occupation": {
          "学生": 30,
          "工程师": 25,
          "其他": 45
        }
      }
    }
  }
  ```
