const api = require('../../utils/api');

/**
 * 智能产品搜索输入组件
 * 支持实时搜索、防抖处理、候选列表展示等功能
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 输入框的值
    value: {
      type: String,
      value: ''
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '请输入产品名称，如：iPhone 15 Pro'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 搜索结果数量限制
    limit: {
      type: Number,
      value: 8
    },
    // 产品类别筛选
    category: {
      type: String,
      value: ''
    },
    // 是否显示清除按钮
    clearable: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 输入框的实际值
    inputValue: '',
    // 搜索建议列表
    suggestions: [],
    // 是否显示候选列表
    showDropdown: false,
    // 是否正在搜索
    loading: false,
    // 搜索关键词
    searchKeyword: '',
    // 是否获得焦点
    focused: false,
    // 下拉框的定位信息
    dropdownStyle: '',
    // 是否使用固定定位模式
    useFixedPosition: false,
    // 用户是否正在触摸下拉框
    touchingDropdown: false,
    // 下拉框隐藏定时器
    hideTimer: null,
    // 组件唯一标识
    componentId: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 组件内部点击事件（防止冒泡到全局处理）
     */
    onComponentTap(e) {
      console.log('组件内部点击');
      // 阻止事件传播，避免触发全局点击隐藏
      e.stopPropagation();
      return false;
    },

    /**
     * 计算下拉框的定位
     */
    calculateDropdownPosition() {
      if (!this.data.showDropdown) {
        return;
      }

      const query = this.createSelectorQuery();
      query.select('.search-input-container').boundingClientRect((rect) => {
        if (rect) {
          // 获取页面滚动信息
          wx.getSystemInfo({
            success: (systemInfo) => {
              const windowHeight = systemInfo.windowHeight;
              const dropdownHeight = 360; // 最大高度 360rpx
              const rpxToPx = systemInfo.windowWidth / 750; // rpx到px的转换比例
              const dropdownHeightPx = dropdownHeight * rpxToPx;
              
              // 判断下方空间是否足够
              const spaceBelow = windowHeight - rect.bottom;
              const hasEnoughSpaceBelow = spaceBelow >= dropdownHeightPx;
              
              // 决定是否使用固定定位
              const shouldUseFixed = rect.top < 50 || !hasEnoughSpaceBelow;
              
              if (shouldUseFixed) {
                // 使用固定定位，避免滚动时分离
                const dropdownStyle = `
                  position: fixed;
                  top: ${rect.bottom}px;
                  left: ${rect.left}px;
                  width: ${rect.width}px;
                  z-index: 99999;
                `;
                this.setData({
                  dropdownStyle: dropdownStyle,
                  useFixedPosition: true
                });
              } else {
                // 使用相对定位
                this.setData({
                  dropdownStyle: '',
                  useFixedPosition: false
                });
              }
            }
          });
        }
      }).exec();
    },

    /**
     * 页面滚动处理函数
     */
    onPageScroll() {
      if (this.data.showDropdown && this.data.useFixedPosition) {
        // 重新计算位置
        this.calculateDropdownPosition();
      }
    },

    /**
     * 隐藏下拉框
     */
    hideDropdown() {
      // 清除隐藏定时器
      this.cancelHideDropdown();
      
      // 解绑全局点击事件
      this.unbindGlobalTapEvent();
      
      this.setData({
        showDropdown: false,
        useFixedPosition: false,
        dropdownStyle: '',
        touchingDropdown: false
      });
    },

    /**
     * 显示下拉框
     */
    showDropdown() {
      if (this.data.suggestions.length > 0) {
        this.setData({
          showDropdown: true
        });
        
        // 绑定全局点击事件
        this.bindGlobalTapEvent();
        
        // 延迟计算位置，确保DOM已更新
        setTimeout(() => {
          this.calculateDropdownPosition();
        }, 50);
      }
    },

    /**
     * 输入框获得焦点
     */
    onFocus(e) {
      console.log('输入框获得焦点');
      
      // 取消可能存在的隐藏定时器
      this.cancelHideDropdown();
      
      this.setData({
        focused: true
      });
      
      // 如果有搜索结果，显示下拉列表
      if (this.data.inputValue.trim() && this.data.suggestions.length > 0) {
        this.showDropdown();
      }
      
      // 监听页面滚动事件
      this.bindPageScrollEvent();
    },

    /**
     * 输入框失去焦点
     */
    onBlur(e) {
      console.log('输入框失去焦点, touchingDropdown:', this.data.touchingDropdown);
      
      // 如果用户正在触摸下拉框，稍后再检查
      if (this.data.touchingDropdown) {
        // 给触摸操作一些时间，然后重新检查
        setTimeout(() => {
          if (!this.data.touchingDropdown && !this.data.focused) {
            this.scheduleHideDropdown(100);
          }
        }, 150);
        return;
      }
      
      // 正常情况下延迟隐藏，给用户点击下拉选项的时间
      this.scheduleHideDropdown(200);
    },

    /**
     * 安排隐藏下拉框
     */
    scheduleHideDropdown(delay = 500) {
      // 清除之前的定时器
      if (this.data.hideTimer) {
        clearTimeout(this.data.hideTimer);
      }
      
      const timer = setTimeout(() => {
        // 再次检查是否正在触摸下拉框
        if (!this.data.touchingDropdown) {
          this.setData({
            focused: false
          });
          this.hideDropdown();
          this.unbindPageScrollEvent();
        }
      }, delay);
      
      this.setData({
        hideTimer: timer
      });
    },

    /**
     * 取消隐藏下拉框
     */
    cancelHideDropdown() {
      if (this.data.hideTimer) {
        clearTimeout(this.data.hideTimer);
        this.setData({
          hideTimer: null
        });
      }
    },

    /**
     * 下拉框触摸开始
     */
    onDropdownTouchStart(e) {
      console.log('下拉框触摸开始');
      this.setData({
        touchingDropdown: true
      });
      this.cancelHideDropdown();
      // 阻止事件冒泡，避免触发页面其他事件
      e.stopPropagation();
      return false;
    },

    /**
     * 下拉框触摸结束
     */
    onDropdownTouchEnd(e) {
      console.log('下拉框触摸结束');
      // 延迟设置touchingDropdown为false，给点击事件时间执行
      setTimeout(() => {
        this.setData({
          touchingDropdown: false
        });
        console.log('touchingDropdown 设置为 false');
      }, 100);
      
      // 阻止事件冒泡
      e.stopPropagation();
      return false;
    },

    /**
     * 下拉框触摸移动
     */
    onDropdownTouchMove(e) {
      // 阻止事件冒泡，避免触发页面滚动等其他事件
      e.stopPropagation();
      return false;
    },

    /**
     * 处理全局点击事件（点击外部区域隐藏下拉框）
     */
    onGlobalTap(e) {
      console.log('全局点击事件触发', e);
      
      // 如果下拉框不显示，无需处理
      if (!this.data.showDropdown) {
        return;
      }

      // 检查点击的目标元素是否在组件内部
      const target = e.target || e.currentTarget;
      if (target && target.dataset) {
        // 检查是否点击在本组件内
        const clickedComponentId = target.dataset.componentId;
        if (clickedComponentId === this.data.componentId) {
          console.log('点击在组件内部，不隐藏');
          return;
        }
        
        // 检查是否点击在下拉框相关的元素上
        const clickedClass = target.className || '';
        if (clickedClass.includes('suggestion') || 
            clickedClass.includes('dropdown') ||
            clickedClass.includes('search-input')) {
          console.log('点击在下拉框相关元素上，不隐藏');
          return;
        }
      }

      // 如果有触摸位置信息，使用位置判断
      if (e.detail && typeof e.detail.x === 'number' && typeof e.detail.y === 'number') {
        const query = this.createSelectorQuery();
        query.select('.product-search-input').boundingClientRect((rect) => {
          if (rect) {
            const { x, y } = e.detail;
            const isInsideComponent = x >= rect.left && 
                                    x <= rect.right && 
                                    y >= rect.top && 
                                    y <= rect.bottom;
            
            console.log('点击位置:', { x, y });
            console.log('组件位置:', rect);
            console.log('是否在组件内:', isInsideComponent);
            
            // 如果点击在组件外部，隐藏下拉框
            if (!isInsideComponent) {
              console.log('点击外部区域，隐藏下拉框');
              this.hideDropdown();
              this.setData({
                focused: false
              });
              this.unbindPageScrollEvent();
            }
          }
        }).exec();
      } else {
        // 没有位置信息时，默认认为是外部点击
        console.log('无位置信息，默认隐藏下拉框');
        this.hideDropdown();
        this.setData({
          focused: false
        });
        this.unbindPageScrollEvent();
      }
    },

    /**
     * 绑定全局点击事件（优化版）
     */
    bindGlobalTapEvent() {
      // 获取页面实例
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      if (currentPage && !this.globalTapBound) {
        console.log('绑定全局点击事件');
        
        // 方法1：使用页面onTap（如果页面定义了的话）
        this.originalOnTap = currentPage.onTap;
        currentPage.onTap = (e) => {
          if (this.originalOnTap) {
            this.originalOnTap.call(currentPage, e);
          }
          this.onGlobalTap(e);
        };

        // 方法2：使用WXS捕获touch事件（备用方案）
        try {
          // 绑定到页面根元素的touchend事件
          const pageElement = currentPage.selectComponent('.page') || currentPage;
          if (pageElement && pageElement.bindTouchEnd) {
            this.originalPageTouchEnd = pageElement.onTouchEnd;
            pageElement.onTouchEnd = (e) => {
              if (this.originalPageTouchEnd) {
                this.originalPageTouchEnd.call(pageElement, e);
              }
              
              // 模拟tap事件，延迟一点执行避免与真实tap冲突
              setTimeout(() => {
                this.onGlobalTap({
                  target: e.target,
                  detail: e.changedTouches && e.changedTouches[0] ? {
                    x: e.changedTouches[0].clientX,
                    y: e.changedTouches[0].clientY
                  } : {}
                });
              }, 10);
            };
          }
        } catch (error) {
          console.warn('绑定touch事件失败:', error);
        }
        
        this.globalTapBound = true;
      }
    },

    /**
     * 解绑全局点击事件（优化版）
     */
    unbindGlobalTapEvent() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      if (currentPage && this.globalTapBound) {
        console.log('解绑全局点击事件');
        
        // 恢复页面原始onTap方法
        if (this.originalOnTap !== undefined) {
          currentPage.onTap = this.originalOnTap;
          this.originalOnTap = null;
        }
        
        // 恢复页面原始touchend方法
        try {
          const pageElement = currentPage.selectComponent('.page') || currentPage;
          if (pageElement && this.originalPageTouchEnd !== undefined) {
            pageElement.onTouchEnd = this.originalPageTouchEnd;
            this.originalPageTouchEnd = null;
          }
        } catch (error) {
          console.warn('解绑touch事件失败:', error);
        }
        
        this.globalTapBound = false;
      }
    },

    /**
     * 绑定页面滚动事件
     */
    bindPageScrollEvent() {
      // 获取页面实例
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      if (currentPage && !this.pageScrollBound) {
        // 保存原始的onPageScroll方法
        this.originalOnPageScroll = currentPage.onPageScroll;
        
        // 绑定新的滚动监听
        currentPage.onPageScroll = (e) => {
          // 调用原始的onPageScroll方法
          if (this.originalOnPageScroll) {
            this.originalOnPageScroll.call(currentPage, e);
          }
          // 调用组件的滚动处理
          this.onPageScroll(e);
        };
        
        this.pageScrollBound = true;
      }
    },

    /**
     * 解绑页面滚动事件
     */
    unbindPageScrollEvent() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      if (currentPage && this.pageScrollBound) {
        // 恢复原始的onPageScroll方法
        currentPage.onPageScroll = this.originalOnPageScroll || null;
        this.pageScrollBound = false;
        this.originalOnPageScroll = null;
      }
    },

    /**
     * 监听输入变化
     */
    onInput(e) {
      const value = e.detail.value;
      this.setData({
        inputValue: value
      });

      // 触发父组件的input事件
      this.triggerEvent('input', {
        value: value
      });

      // 如果输入为空，清空搜索结果
      if (!value.trim()) {
        this.setData({
          suggestions: [],
          searchKeyword: ''
        });
        this.hideDropdown();
        return;
      }

      // 防抖搜索
      this.debounceSearch(value.trim());
    },

    /**
     * 防抖搜索函数
     */
    debounceSearch(keyword) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器
      this.searchTimer = setTimeout(() => {
        this.performSearch(keyword);
      }, 300); // 300ms 防抖延迟
    },

    /**
     * 执行搜索
     */
    async performSearch(keyword) {
      if (!keyword || keyword.length < 1) {
        return;
      }

      // 避免重复搜索相同关键词
      if (keyword === this.data.searchKeyword) {
        return;
      }

      try {
        this.setData({
          loading: true,
          searchKeyword: keyword
        });

        // 调用产品搜索API
        const result = await api.product.searchProductNames(
          keyword, 
          this.data.limit, 
          this.data.category
        );

        console.log('产品搜索结果:', result);

        if (result.success && result.data && result.data.productNames) {
          // 将后端返回的productNames转换为组件所需的suggestions格式（简化版）
          const suggestions = result.data.productNames.map((productName, index) => ({
            id: `product_${index}_${Date.now()}`, // 生成唯一ID
            name: productName
          }));

          this.setData({
            suggestions: suggestions,
            loading: false
          });

          // 如果输入框有焦点，显示下拉框
          if (suggestions.length > 0 && this.data.focused) {
            this.showDropdown();
          } else {
            this.hideDropdown();
          }
        } else {
          this.setData({
            suggestions: [],
            loading: false
          });
          this.hideDropdown();
        }
      } catch (error) {
        console.error('产品搜索失败:', error);
        this.setData({
          suggestions: [],
          loading: false
        });
        this.hideDropdown();
        
        // 显示错误提示
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 选择产品
     */
    onSelectProduct(e) {
      const product = e.currentTarget.dataset.product;
      
      // 立即停止所有隐藏定时器
      this.cancelHideDropdown();
      
      this.setData({
        inputValue: product.name,
        suggestions: [],
        touchingDropdown: false
      });
      
      this.hideDropdown();

      // 触发父组件的选择事件
      this.triggerEvent('select', {
        product: product,
        value: product.name
      });

      // 触发input事件保持数据同步
      this.triggerEvent('input', {
        value: product.name
      });
    },

    /**
     * 清空输入
     */
    onClear() {
      // 停止所有定时器
      this.cancelHideDropdown();
      
      this.setData({
        inputValue: '',
        suggestions: [],
        searchKeyword: '',
        touchingDropdown: false
      });
      
      this.hideDropdown();

      // 触发父组件的清空事件
      this.triggerEvent('clear');
      this.triggerEvent('input', {
        value: ''
      });
    },

    /**
     * 手动触发搜索（用于刷新）
     */
    refresh() {
      if (this.data.inputValue.trim()) {
        this.performSearch(this.data.inputValue.trim());
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件实例刚刚被创建时执行
     */
    created() {
      this.searchTimer = null;
      this.pageScrollBound = false;
      this.originalOnPageScroll = null;
      this.globalTapBound = false;
      this.originalOnTap = null;
      this.originalPageTouchEnd = null;
      
      // 生成组件唯一标识
      this.setData({
        componentId: `product-search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      });
    },

    /**
     * 组件实例进入页面节点树时执行
     */
    attached() {
      // 初始化输入值
      this.setData({
        inputValue: this.properties.value
      });
    },

    /**
     * 组件实例被从页面节点树移除时执行
     */
    detached() {
      // 清理定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
        this.searchTimer = null;
      }
      
      // 清理隐藏定时器
      this.cancelHideDropdown();
      
      // 解绑页面滚动事件
      this.unbindPageScrollEvent();
      
      // 解绑全局点击事件
      this.unbindGlobalTapEvent();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'value'(newVal) {
      if (newVal !== this.data.inputValue) {
        this.setData({
          inputValue: newVal || ''
        });
      }
    }
  }
}); 