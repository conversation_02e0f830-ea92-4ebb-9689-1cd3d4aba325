<!--components/feedback-item/feedback-item.wxml-->
<view 
  class="feedback-item"
  bindtap="onFeedbackTap"
>
  <!-- 反馈头部信息 -->
  <view class="feedback-header">
    <view class="type-badge type-{{feedback.type}}">
      {{typeText}}
    </view>
    <view class="status-badge {{statusClass}}">
      {{statusText}}
    </view>
  </view>
  
  <!-- 反馈内容预览 -->
  <view class="feedback-content">
    <text class="content-text">{{feedback.content}}</text>
  </view>
  
  <!-- 图片预览 -->
  <view class="feedback-images" wx:if="{{feedback.images && feedback.images.length > 0}}">
    <block wx:for="{{feedback.images}}" wx:key="index" wx:for-item="image">
      <image 
        src="{{image}}" 
        mode="aspectFill"
        class="image-thumb"
        wx:if="{{index < 3}}"
        catchtap="previewImage"
        data-urls="{{feedback.images}}"
        data-current="{{image}}"
      ></image>
    </block>
    <view class="more-images" wx:if="{{feedback.images.length > 3}}">
      +{{feedback.images.length - 3}}
    </view>
  </view>
  
  <!-- 反馈底部信息 -->
  <view class="feedback-footer">
    <text class="create-time">{{formattedTime}}</text>
    <view class="reply-info" wx:if="{{feedback.adminReply && feedback.adminReply.content}}">
      <text class="reply-icon">💬</text>
      <text class="reply-text">已回复</text>
    </view>
  </view>
</view> 