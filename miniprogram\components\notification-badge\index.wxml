<view class="notification-badge" bindtap="onClick">
  <view class="icon-container">
    <!-- 通知图标 -->
    <view class="notification-icon" 
      style="width: {{size}}rpx; height: {{size}}rpx; color: {{active ? '#3B7ADB' : color}}">
      <view class="icon-inner"></view>
    </view>
    
    <!-- 未读数量角标 -->
    <view class="badge" wx:if="{{unreadCount > 0}}">
      <text>{{unreadCount > 99 ? '99+' : unreadCount}}</text>
    </view>
    
    <!-- 仅显示小红点 -->
    <view class="dot" wx:elif="{{showDot}}"></view>
  </view>
  <slot></slot>
</view> 