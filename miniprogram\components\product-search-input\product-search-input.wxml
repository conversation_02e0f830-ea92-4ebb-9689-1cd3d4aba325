<!--components/product-search-input/product-search-input.wxml-->
<view 
  class="product-search-input {{focused ? 'focused' : ''}}" 
  data-component-id="{{componentId}}"
  catchtap="onComponentTap"
>
  <!-- 输入框容器 -->
  <view class="search-input-container">
    <!-- 搜索图标 -->
    <view class="search-icon">
      <text class="iconfont icon-search"></text>
    </view>
    
    <!-- 输入框 -->
    <input
      class="search-input"
      type="text"
      placeholder="{{placeholder}}"
      value="{{inputValue}}"
      disabled="{{disabled}}"
      bindinput="onInput"
      bindfocus="onFocus"
      bindblur="onBlur"
    />
    
    <!-- 加载状态 -->
    <view class="loading-indicator" wx:if="{{loading}}">
      <text class="loading-spinner"></text>
    </view>
    
    <!-- 清除按钮 -->
    <view 
      class="clear-btn" 
      wx:if="{{clearable && inputValue && !loading}}"
      bindtap="onClear"
    >
      <text class="iconfont icon-close"></text>
    </view>
  </view>
  
  <!-- 候选列表下拉框 -->
  <view 
    class="suggestions-dropdown" 
    wx:if="{{showDropdown}}"
    style="{{dropdownStyle}}"
    bindtouchstart="onDropdownTouchStart"
    bindtouchend="onDropdownTouchEnd"
    bindtouchmove="onDropdownTouchMove"
    catchtouchstart="onDropdownTouchStart"
    catchtouchend="onDropdownTouchEnd"
    catchtouchmove="onDropdownTouchMove"
  >
    <scroll-view 
      class="suggestions-container" 
      scroll-y="true"
      show-scrollbar="false"
      bindtouchstart="onDropdownTouchStart"
      bindtouchend="onDropdownTouchEnd"
      catchtouchstart="onDropdownTouchStart"
      catchtouchend="onDropdownTouchEnd"
    >
      <!-- 搜索建议列表 -->
      <view class="suggestions-list" wx:if="{{suggestions.length > 0}}">
        <view 
          class="suggestion-item" 
          wx:for="{{suggestions}}" 
          wx:key="id"
          data-product="{{item}}"
          bindtap="onSelectProduct"
          catchtap="onSelectProduct"
        >
          <!-- 产品图标 -->
          <view class="suggestion-icon">
            <text class="iconfont icon-product"></text>
          </view>
          
          <!-- 产品信息 - 简化版，只显示产品名称 -->
          <view class="suggestion-info">
            <view class="suggestion-name">{{item.name}}</view>
          </view>
          
          <!-- 选择箭头 -->
          <view class="suggestion-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>
      </view>
      
      <!-- 无搜索结果 -->
      <view class="no-results" wx:if="{{suggestions.length === 0 && !loading && searchKeyword}}">
        <view class="no-results-icon">
          <text class="iconfont icon-search"></text>
        </view>
        <view class="no-results-text">未找到相关产品</view>
        <view class="no-results-tip">请尝试其他关键词或检查拼写</view>
      </view>
      
      <!-- 搜索中状态 -->
      <view class="searching-state" wx:if="{{loading && searchKeyword}}">
        <view class="searching-icon">
          <text class="loading-spinner"></text>
        </view>
        <view class="searching-text">正在搜索产品...</view>
      </view>
    </scroll-view>
  </view>
</view> 