# 搜索功能API文档

## 功能概述
搜索功能允许用户通过关键词在问题标题、使用场景、关键因素中进行全文搜索，支持标签过滤和分页。

## API接口

### 搜索问题
**接口地址**: `GET /api/v1/community/questions/search`

**权限要求**: 需要用户登录认证

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| keyword | string | 否 | - | 搜索关键词，最多100个字符 |
| tags | string/array | 否 | - | 标签过滤，可选值: "手机", "电脑" |
| status | string | 否 | open | 问题状态，可选值: "open", "closed" |
| page | number | 否 | 1 | 页码，最小值为1 |
| limit | number | 否 | 10 | 每页数量，范围1-50 |

**请求示例**:
```bash
# 基础搜索
GET /api/v1/community/questions/search?keyword=iPhone手机

# 带标签过滤的搜索  
GET /api/v1/community/questions/search?keyword=游戏&tags=电脑

# 多标签搜索
GET /api/v1/community/questions/search?keyword=性价比&tags[]=手机&tags[]=电脑

# 分页搜索
GET /api/v1/community/questions/search?keyword=推荐&page=2&limit=20
```

**响应数据**:
```json
{
  "success": true,
  "code": 200,
  "message": "搜索问题成功",
  "data": {
    "questions": [
      {
        "id": "问题ID",
        "title": "问题标题",
        "scene": "使用场景",
        "keyFactors": "关键因素",
        "budget": {
          "min": 1000,
          "max": 5000,
          "currency": "CNY"
        },
        "tags": ["手机"],
        "options": [
          {
            "id": "选项ID",
            "content": "选项内容",
            "voteCount": 10
          }
        ],
        "totalVotes": 25,
        "commentCount": 5,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "expiryTime": null,
        "status": "open",
        "requireReason": false,
        "user": {
          "id": "用户ID",
          "nickname": "用户昵称",
          "avatar": "头像URL"
        },
        "isAnonymous": false,
        "relevanceScore": 1.2
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "limit": 10,
      "pages": 5
    },
    "searchKeyword": "iPhone手机"
  },
  "timestamp": 1704067200000
}
```

## 搜索特性

### 1. 文本搜索
- **搜索范围**: 问题标题、使用场景、关键因素
- **权重设置**: 
  - 标题权重: 10 (最高)
  - 关键因素权重: 5 (中等) 
  - 使用场景权重: 1 (最低)
- **相关度排序**: 有关键词时按相关度和创建时间排序

### 2. 过滤条件
- **标签过滤**: 支持单个或多个标签筛选
- **状态过滤**: 默认只搜索开放状态的问题
- **分页支持**: 避免大量数据影响性能

### 3. 搜索策略
- 空关键词时：按创建时间倒序返回所有符合条件的问题
- 有关键词时：按文本相关度和创建时间综合排序
- 支持中文分词和模糊匹配

## 使用建议

1. **前端实现**：
   - 添加防抖处理，避免频繁请求
   - 显示搜索历史和热门搜索
   - 支持搜索结果高亮显示

2. **性能优化**：
   - 合理设置limit参数
   - 考虑添加搜索缓存
   - 监控搜索性能

3. **用户体验**：
   - 提供搜索建议
   - 显示搜索结果数量
   - 支持搜索结果排序切换 