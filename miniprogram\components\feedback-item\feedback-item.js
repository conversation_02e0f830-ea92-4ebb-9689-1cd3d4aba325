Component({
  /**
   * 组件的属性列表
   */
  properties: {
    feedback: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    typeText: '',
    statusText: '',
    statusClass: '',
    formattedTime: ''
  },

  /**
   * 监听器
   */
  observers: {
    'feedback': function(feedback) {
      if (feedback && Object.keys(feedback).length > 0) {
        this.setData({
          typeText: this.getTypeText(feedback.type),
          statusText: this.getStatusText(feedback.status),
          statusClass: this.getStatusClass(feedback.status),
          formattedTime: this.formatTime(feedback.createdAt)
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击反馈项时的处理
     */
    onFeedbackTap: function() {
      const feedbackId = this.properties.feedback._id;
      this.triggerEvent('feedbacktap', { id: feedbackId });
    },

    /**
     * 预览图片
     */
    previewImage: function(e) {
      const urls = e.currentTarget.dataset.urls;
      const current = e.currentTarget.dataset.current;
      
      wx.previewImage({
        urls: urls,
        current: current
      });
      // 阻止事件冒泡，防止触发反馈项点击
      return false;
    },

    /**
     * 获取反馈类型显示文本
     */
    getTypeText: function(type) {
      const typeMap = {
        'bug': '问题反馈',
        'suggestion': '建议反馈', 
        'question': '疑问咨询',
        'other': '其他反馈'
      };
      return typeMap[type] || '其他反馈';
    },

    /**
     * 获取反馈状态显示文本
     */
    getStatusText: function(status) {
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'resolved': '已解决',
        'rejected': '已拒绝'
      };
      return statusMap[status] || '待处理';
    },

    /**
     * 获取反馈状态样式类名
     */
    getStatusClass: function(status) {
      const classMap = {
        'pending': 'status-pending',
        'processing': 'status-processing',
        'resolved': 'status-resolved',
        'rejected': 'status-rejected'
      };
      return classMap[status] || 'status-pending';
    },

    /**
     * 格式化时间
     */
    formatTime: function(time) {
      if (!time) return '';
      
      const date = new Date(time);
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      
      // 不到1分钟
      if (diff < 60000) {
        return '刚刚';
      }
      
      // 不到1小时
      if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前';
      }
      
      // 不到1天
      if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前';
      }
      
      // 不到7天
      if (diff < 604800000) {
        return Math.floor(diff / 86400000) + '天前';
      }
      
      // 超过7天显示具体日期
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    }
  }
}) 