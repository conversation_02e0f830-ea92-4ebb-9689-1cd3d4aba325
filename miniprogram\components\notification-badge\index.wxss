/* 通知角标组件样式 */
.notification-badge {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
}

.icon-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.notification-icon {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.notification-icon .icon-inner {
  width: 60%;
  height: 60%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.badge {
  position: absolute;
  top: -6rpx;
  right: -10rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  z-index: 10;
}

.dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  z-index: 10;
} 