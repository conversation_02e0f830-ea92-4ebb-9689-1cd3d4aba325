# OSS部署命令指南

## 在服务器上安装OSS依赖

### 1. 进入项目目录
```bash
cd /path/to/your/xuanxuan/server
```

### 2. 安装阿里云OSS SDK
```bash
npm install ali-oss --save
```

### 3. 安装图片处理依赖（如果还没有安装）
```bash
npm install jimp --save
```

### 4. 创建配置文件目录（如果不存在）
```bash
mkdir -p src/config
```

### 5. 创建.env文件（复制以下内容并修改配置）
```bash
cat > .env << 'EOF'
# 服务器配置
PORT=3000
NODE_ENV=production
BASE_URL=https://your-domain.com

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/xuanxuan

# JWT配置
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRE=7d

# 存储配置 - 改为 'oss' 启用OSS存储
STORAGE_TYPE=oss

# 阿里云OSS配置（请填入你的真实配置）
OSS_ACCESS_KEY_ID=your-access-key-id
OSS_ACCESS_KEY_SECRET=your-access-key-secret
OSS_BUCKET=your-bucket-name
OSS_REGION=oss-cn-hangzhou
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_DOMAIN=https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com
OSS_FOLDER=xuanxuan-app

# 微信小程序配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret
EOF
```

### 6. 编辑.env文件，填入真实配置
```bash
nano .env
# 或者使用 vim .env
```

### 7. 重启服务器
```bash
# 如果使用PM2
pm2 restart all

# 如果使用systemd
sudo systemctl restart your-app-name

# 如果直接运行
pkill -f node
npm start
```

## 验证OSS配置

### 检查配置是否正确
```bash
# 查看日志确认OSS初始化状态
pm2 logs

# 或者
tail -f /path/to/your/log/file
```

### 测试上传功能
使用Postman或curl测试图片上传接口：

```bash
curl -X POST \
  http://your-domain.com/api/v1/feedback/upload-images \
  -H 'Authorization: Bearer your-jwt-token' \
  -F 'images=@/path/to/test/image.jpg'
```

## 常用命令

### 查看包安装状态
```bash
npm list ali-oss
npm list jimp
```

### 查看环境变量
```bash
cat .env
```

### 重新安装依赖
```bash
rm -rf node_modules package-lock.json
npm install
```

### 查看服务器状态
```bash
pm2 status
pm2 logs your-app-name --lines 50
```

## 故障排查

### 如果OSS上传失败
1. 检查AccessKey配置是否正确
2. 确认Bucket权限设置
3. 检查网络连接
4. 查看详细日志

### 如果图片无法访问
1. 确认Bucket为"公共读"权限
2. 检查跨域设置
3. 验证域名配置

### 回滚到本地存储
```bash
# 编辑.env文件
nano .env

# 将 STORAGE_TYPE=oss 改为 STORAGE_TYPE=local
# 保存并重启服务
pm2 restart all
``` 