# AI推荐产品选项 API 文档

## 概述

AI推荐产品选项功能允许用户在发布问题时，根据问题描述、使用场景、关键考量因素和筛选条件，智能推荐2-5个最合适的产品作为问题选项。

## 功能特点

- **智能分析**：基于用户需求和产品参数进行智能匹配
- **多维筛选**：支持品牌、价格等多维度筛选
- **专业推荐**：AI分析用户场景给出专业建议
- **数据真实**：推荐的产品均来自真实的产品数据库

## API 接口

### 1. AI推荐产品选项

**接口地址**：`POST /api/v1/community/questions/ai-recommend`

**请求头**：
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**：

```json
{
  "questionInfo": {
    "title": "帮我选择一款适合游戏的手机",
    "scene": "主要用于手机游戏，偶尔拍照，日常使用",
    "keyFactors": "性能、续航、散热",
    "budget": {
      "min": 2000,
      "max": 5000,
      "currency": "CNY"
    }
  },
      "filterOptions": {
      "productType": "phone",
      "brands": ["小米", "荣耀", "realme"],
      "budget": {
        "min": 2000,
        "max": 5000,
        "currency": "CNY"
      }
    }
}
```

**参数说明**：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| questionInfo | Object | 是 | 问题信息 |
| questionInfo.title | String | 是 | 问题标题，最多50字符 |
| questionInfo.scene | String | 否 | 使用场景，最多500字符 |
| questionInfo.keyFactors | String | 否 | 关键考量因素，最多200字符 |
| questionInfo.budget | Object | 否 | 预算区间 |
| filterOptions | Object | 是 | 筛选条件 |
| filterOptions.productType | String | 是 | 产品类型：phone/laptop |
| filterOptions.brands | Array | 是 | 品牌列表，必填，最多10个 |
| filterOptions.budget | Object | 否 | 价格筛选 |

**成功响应**：

```json
{
  "success": true,
  "message": "AI产品推荐成功",
  "data": {
    "recommendedProducts": [
      {
        "skuId": "PHONE_2025_001",
        "skuName": "小米14 Pro 512GB",
        "brand": "小米",
        "price": 4299,
        "priceWan": "42.99万",
        "imageUrl": "https://example.com/xiaomi14pro.jpg",
        "recommendReason": "骁龙8 Gen3旗舰性能，游戏帧率稳定，5000mAh大电池续航无忧",
        "highlights": ["游戏性能强劲", "续航持久", "快充便利", "散热优秀"],
        "specs": {
          "processor": "骁龙8 Gen3",
          "ram": "12GB",
          "storage": "512GB",
          "screenSize": "6.73英寸",
          "battery": "4880mAh",
          "camera": "5000万像素徕卡光学镜头"
        }
      }
    ],
    "aiAnalysis": "根据您的游戏需求和预算，推荐以下产品：\n\n1. 小米14 Pro - 骁龙8 Gen3性能强劲...",
    "filterInfo": {
      "productType": "phone",
      "brands": ["小米", "荣耀", "realme"]
    },
    "meta": {
      "totalCandidates": 15,
      "recommendedCount": 3,
      "processingTime": 2500
    }
  }
}
```

**响应字段说明**：

| 字段 | 类型 | 说明 |
|------|------|------|
| data.recommendedProducts | Array | 推荐产品列表（2-5个） |
| recommendedProducts[].skuId | String | 产品SKU唯一标识符 |
| recommendedProducts[].skuName | String | 产品完整名称 |
| recommendedProducts[].brand | String | 产品品牌 |
| recommendedProducts[].price | Number | 产品价格（单位：元） |
| recommendedProducts[].priceWan | String | 价格万元显示格式 |
| recommendedProducts[].imageUrl | String | 产品图片URL |
| recommendedProducts[].recommendReason | String | AI推荐该产品的具体理由（30-50字） |
| recommendedProducts[].highlights | Array | 产品主要亮点和优势（2-4个） |
| recommendedProducts[].specs | Object | 产品主要规格参数 |
| data.aiAnalysis | String | AI对用户需求的整体分析和推荐总结 |
| data.filterInfo | Object | 使用的筛选条件信息 |
| data.meta | Object | 推荐元数据（候选数量、处理时间等） |

**错误响应**：

```json
{
  "success": false,
  "message": "请求参数错误",
  "error": "问题标题不能为空"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 用户未登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 503 | AI推荐服务不可用 |

## 使用示例

### 手机推荐示例

```javascript
const response = await fetch('/api/v1/community/questions/ai-recommend', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    questionInfo: {
      title: "学生党求推荐性价比手机",
      scene: "主要用于学习、刷视频、轻度游戏",
      keyFactors: "性价比、续航、拍照",
      budget: { min: 1000, max: 2000, currency: "CNY" }
    },
    filterOptions: {
      productType: "phone",
      brands: ["小米", "荣耀", "realme"]
    }
  })
});
```

### 笔记本推荐示例

```javascript
const response = await fetch('/api/v1/community/questions/ai-recommend', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    questionInfo: {
      title: "程序员工作笔记本推荐",
      scene: "主要用于编程开发、多任务处理",
      keyFactors: "性能、内存、散热、便携性",
      budget: { min: 8000, max: 15000, currency: "CNY" }
    },
    filterOptions: {
      productType: "laptop",
      brands: ["苹果", "华为", "联想"]
    }
  })
});
```

## 最佳实践

1. **明确需求**：在问题描述中清楚说明使用场景和关键需求
2. **合理预算**：设置合理的预算范围以获得更精准的推荐
3. **品牌筛选**：选择合适的品牌进行筛选
4. **错误处理**：做好错误处理，提供用户友好的提示信息

## 限制说明

- 每次最多推荐5个产品
- 问题标题不超过50字符
- 使用场景描述不超过500字符
- 关键考量因素不超过200字符
- 最多选择10个品牌（必填）

## 技术架构

```
用户请求 → 参数验证 → 数据库筛选 → AI分析 → 结果解析 → 响应返回
```

1. **参数验证**：使用Joi进行严格的参数验证
2. **数据库筛选**：根据品牌、价格等条件初步筛选
3. **AI分析**：将用户需求和候选产品发送给AI进行智能推荐
4. **结果解析**：解析AI推荐结果，格式化产品信息
5. **响应返回**：返回推荐产品列表和AI分析说明

## 数据流图

```
[用户输入] → [参数验证] → [数据库查询] → [AI推荐] → [结果格式化] → [返回响应]
     ↓              ↓              ↓           ↓            ↓
[问题信息]     [筛选条件]     [候选产品]   [推荐结果]   [格式化产品]
[筛选条件]     [验证结果]     [产品列表]   [AI分析]     [响应数据]
``` 