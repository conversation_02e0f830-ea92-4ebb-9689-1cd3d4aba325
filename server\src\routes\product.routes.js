const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { searchProductsController } = require('../controllers/product/productSearchController');
const { compareProductsV4Parallel } = require('../controllers/product/productCompareParallelController');
const { createComparisonTask, getTaskStatus, getUserTasks } = require('../controllers/product/productCompareAsyncController');
const { protect } = require('../middlewares/auth');
const rateLimit = require('express-rate-limit');

const compareRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 20, // 每分钟最多20次对比请求（AI分析比较耗时）
  message: {
    success: false,
    code: 429,
    message: '对比请求过于频繁，请稍后重试',
    timestamp: Date.now()
  },
  standardHeaders: true,
  legacyHeaders: false
});

const asyncCompareRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 每分钟最多10次异步对比请求（更严格的限制）
  message: {
    success: false,
    code: 429,
    message: '异步对比请求过于频繁，请稍后重试',
    timestamp: Date.now()
  },
  standardHeaders: true,
  legacyHeaders: false
});

const searchRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 60, // 每分钟最多60次搜索请求（搜索响应快，可以允许更多请求）
  message: {
    success: false,
    code: 429,
    message: '搜索请求过于频繁，请稍后重试',
    timestamp: Date.now()
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route   GET /api/v1/products/params
 * @desc    获取产品详细参数
 * @access  Public
 */
router.get('/params', searchRateLimit, productController.getProductParams);

/**
 * @route   GET /api/v1/products/search
 * @desc    智能搜索产品
 * @access  Public
 */
router.get('/search-products', searchRateLimit, searchProductsController);

/**
 * @route   GET /api/v1/products/search-names
 * @desc    搜索产品名称（用于自动完成）
 * @access  Public
 */
router.get('/search-names', searchRateLimit, productController.searchProductNames);

/**
 * @route   GET /api/v1/products/query
 * @desc    查询产品库产品
 * @access  Public
 */
router.get('/query', searchRateLimit, productController.queryProducts);

/**
 * @route   POST /api/v1/products/compare
 * @desc    产品参数对比
 * @access  Public
 */
router.post('/compare', compareRateLimit, productController.compareProducts);

/**
 * @route   POST /api/v1/products/compare-v4
 * @desc    产品参数对比 V4版本 - 基于参数字段提取的智能对比
 * @access  Public
 */
router.post('/compare-v4', compareRateLimit, productController.compareProductsV4);

/**
 * @route   POST /api/v1/products/compare-basic
 * @desc    产品参数基础对比 - 非AI版本，返回原始参数数据
 * @access  Public
 */
router.post('/compare-basic', compareRateLimit, productController.compareProductsBasic);

/**
 * @route   POST /api/v1/products/compare-v4-parallel
 * @desc    产品参数对比 V4并行版本 - 基于参数字段提取的智能对比（并行处理）
 * @access  Public
 */
router.post('/compare-v4-parallel', compareRateLimit, compareProductsV4Parallel);

/**
 * @route   POST /api/v1/products/compare-async
 * @desc    创建异步产品对比任务
 * @access  Private
 */
router.post('/compare-async', protect, asyncCompareRateLimit, createComparisonTask);

/**
 * @route   GET /api/v1/products/compare-async/:taskId
 * @desc    获取异步对比任务状态
 * @access  Private
 */
router.get('/compare-async/:taskId', protect, getTaskStatus);

/**
 * @route   GET /api/v1/products/compare-async
 * @desc    获取用户的异步对比任务列表
 * @access  Private
 */
router.get('/compare-async', protect, getUserTasks);

module.exports = router;