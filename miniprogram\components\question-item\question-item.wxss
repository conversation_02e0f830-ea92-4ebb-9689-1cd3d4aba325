/* 问题列表样式 */
.question-item {
  margin-bottom: 16rpx;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.info {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.time {
  font-size: 22rpx;
  color: #999999;
  margin-top: 6rpx;
}

/* 状态标签 */
.status-tag {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.status-open {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.status-closed {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

/* 问题内容区域 */
.question-content {
  margin-bottom: 16rpx;
  width: 100%;
}

.title {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.tag-required {
  font-size: 20rpx;
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  margin-left: 12rpx;
}

.content {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

/* 图片列表 */
.image-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
  width: 100%;
}

.question-image {
  width: 220rpx;
  height: 220rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}

.question-image.single {
  width: 400rpx;
  max-height: 400rpx;
  height: auto;
}

/* 选项列表 */
.options-list {
  margin-top: 16rpx;
  width: 100%;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  margin-bottom: 12rpx;
  background: #F7F8FC;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333333;
}

.vote-count {
  font-size: 24rpx;
  color: #999999;
}

/* 问题统计数据 */
.question-stats {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
  margin-top: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
  margin-right: 32rpx;
}

.stat-item .iconfont {
  margin-right: 6rpx;
  font-size: 28rpx;
}

/* 查看结果按钮 */
.view-result-btn {
  display: flex;
  align-items: center;
  margin-left: auto;
  background: #1890ff;
  color: #ffffff;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.view-result-btn .iconfont {
  margin-right: 6rpx;
  font-size: 28rpx;
} 