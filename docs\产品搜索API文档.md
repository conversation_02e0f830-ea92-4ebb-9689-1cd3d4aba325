# 产品名称搜索API文档

## 概述

产品名称搜索API为"选选"小程序的产品对比功能提供**产品名称自动完成**支持。用户输入关键词后，系统返回匹配的产品名称列表，用户可直接选择完整的产品名称，简化输入流程。

## 核心特点

🎯 **专注产品名称匹配**：只通过用户输入匹配产品名称  
📝 **简化返回格式**：只返回产品名称字符串，不包含其他产品信息  
⚡ **高效响应**：针对自动完成场景优化，响应速度更快  
🔍 **智能匹配**：支持精确匹配、前缀匹配、包含匹配等多种策略  

## 基本信息

- **请求方式**: `GET`
- **请求路径**: `/api/v1/products/search-names`
- **访问权限**: 公开访问（不需要认证）
- **限流策略**: 每分钟最多60次请求
- **响应时间**: < 100ms（目标）

## 搜索策略

### 🎯 **匹配优先级**
1. **精确匹配** (权重100): 完全匹配产品名称
2. **前缀匹配** (权重80): 产品名称以关键词开头  
3. **包含匹配** (权重60): 产品名称包含关键词
4. **品牌匹配** (权重40): 品牌名称包含关键词（显示该品牌产品）

### 🧮 **相关性计算**
- 匹配类型权重
- 关键词在产品名中的位置（越靠前越好）
- 产品名称长度（较短的更相关）
- 关键词覆盖度（关键词占产品名的比例）

## 请求参数

| 参数名 | 类型 | 是否必需 | 默认值 | 取值范围 | 说明 |
|--------|------|----------|--------|----------|------|
| `keyword` | String | 是 | - | 1-50字符 | 搜索关键词，支持中文和英文 |
| `limit` | Number | 否 | 10 | 1-20 | 返回结果数量限制 |
| `category` | String | 否 | '' | phone/laptop/tablet/headphones/smartwatch/other | 产品类别筛选 |

### 参数说明

#### keyword（搜索关键词）
- **必填参数**，用于匹配产品名称
- 支持中文、英文、数字、特殊字符
- 示例：`华为`、`iPhone`、`Pro`、`Mate 70`

#### limit（结果数量限制）  
- 建议自动完成场景使用5-10个结果
- 移动端建议不超过8个，避免列表过长

#### category（产品类别筛选）
- 可以限制搜索范围，提高匹配精度
- 当明确产品类型时建议使用

## 请求示例

### 基础搜索
```http
GET /api/v1/products/search-names?keyword=华为
```

### 限制结果数量
```http
GET /api/v1/products/search-names?keyword=iPhone&limit=5
```

### 按类别筛选
```http
GET /api/v1/products/search-names?keyword=Pro&category=phone&limit=8
```

### JavaScript调用示例
```javascript
/**
 * 搜索产品名称（自动完成）
 * @param {string} keyword 搜索关键词
 * @param {number} limit 结果数量
 * @param {string} category 产品类别
 * @returns {Promise<Array<string>>} 产品名称列表
 */
async function searchProductNames(keyword, limit = 8, category = '') {
  try {
    const params = new URLSearchParams({ keyword, limit: limit.toString() });
    if (category) params.append('category', category);
    
    const response = await fetch(`/api/v1/products/search-names?${params}`);
    const data = await response.json();
    
    if (data.success) {
      return data.data.productNames; // 直接返回产品名称数组
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('搜索产品名称失败:', error);
    return [];
  }
}

// 使用示例
searchProductNames('华为').then(productNames => {
  productNames.forEach(name => {
    console.log(name); // 直接是产品名称字符串
  });
});
```

### 自动完成组件示例
```javascript
// 防抖搜索函数
const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

// 自动完成组件
class ProductNameAutocomplete {
  constructor(inputElement, suggestionContainer) {
    this.input = inputElement;
    this.suggestions = suggestionContainer;
    this.selectedProducts = [];
    
    this.setupEvents();
  }
  
  setupEvents() {
    // 输入事件（防抖）
    this.input.addEventListener('input', debounce(async (e) => {
      const keyword = e.target.value.trim();
      
      if (keyword.length >= 2) {
        const productNames = await searchProductNames(keyword, 8);
        this.showSuggestions(productNames);
      } else {
        this.hideSuggestions();
      }
    }, 300));
  }
  
  showSuggestions(productNames) {
    this.suggestions.innerHTML = '';
    this.suggestions.style.display = productNames.length > 0 ? 'block' : 'none';
    
    productNames.forEach(productName => {
      const item = document.createElement('div');
      item.className = 'suggestion-item';
      item.textContent = productName;
      item.addEventListener('click', () => this.selectProduct(productName));
      this.suggestions.appendChild(item);
    });
  }
  
  selectProduct(productName) {
    this.input.value = productName; // 直接填充完整产品名称
    this.hideSuggestions();
    
    // 触发选择事件
    this.onProductSelected(productName);
  }
  
  onProductSelected(productName) {
    console.log('用户选择了产品:', productName);
    // 这里可以添加到对比列表或执行其他操作
  }
  
  hideSuggestions() {
    this.suggestions.style.display = 'none';
  }
}

// 初始化自动完成
const autocomplete = new ProductNameAutocomplete(
  document.getElementById('product-input'),
  document.getElementById('suggestions')
);
```

### 小程序调用示例
```javascript
// 微信小程序页面
Page({
  data: {
    searchKeyword: '',
    suggestions: [],
    showSuggestions: false,
    selectedProducts: []
  },
  
  // 输入框输入事件
  onSearchInput: function(e) {
    const keyword = e.detail.value.trim();
    this.setData({ searchKeyword: keyword });
    
    if (keyword.length >= 2) {
      // 防抖处理
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.searchProductNames(keyword);
      }, 300);
    } else {
      this.setData({ suggestions: [], showSuggestions: false });
    }
  },
  
  // 搜索产品名称
  searchProductNames: function(keyword) {
    wx.request({
      url: 'https://your-domain.com/api/v1/products/search-names',
      data: { keyword, limit: 8 },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            suggestions: res.data.data.productNames,
            showSuggestions: res.data.data.productNames.length > 0
          });
        }
      },
      fail: (error) => {
        console.error('搜索失败:', error);
      }
    });
  },
  
  // 选择产品名称
  selectProductName: function(e) {
    const productName = e.currentTarget.dataset.name;
    
    this.setData({
      searchKeyword: productName,
      showSuggestions: false
    });
    
    // 添加到对比列表
    this.addToCompareList(productName);
  },
  
  // 添加到对比列表
  addToCompareList: function(productName) {
    const selectedProducts = this.data.selectedProducts;
    
    if (selectedProducts.length < 5 && !selectedProducts.includes(productName)) {
      selectedProducts.push(productName);
      this.setData({ selectedProducts });
    }
  }
});
```

## 响应格式

### 成功响应 (HTTP 200)

```json
{
  "success": true,
  "code": 200,
  "message": "搜索成功",
  "data": {
    "productNames": [
      "华为 Mate 70 Pro 5G智能手机 12GB+256GB 雅川青",
      "华为 Mate 70 标准版 8GB+256GB 翡冷翠",
      "华为 Mate 70 Pro+ 16GB+512GB 羽砂紫",
      "华为 Mate 70 Ultra 16GB+1TB 玄黑色"
    ],
    "total": 4,
    "searchTime": 89,
    "meta": {
      "keyword": "华为mate70",
      "requestedLimit": 10,
      "category": "phone",
      "description": "输入关键词搜索匹配的产品名称"
    }
  },
  "timestamp": 1705123456789
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `productNames` | Array<String> | 匹配的产品名称列表，按相关性排序 |
| `total` | Number | 返回的产品名称数量 |
| `searchTime` | Number | 搜索耗时（毫秒） |
| `keyword` | String | 实际搜索的关键词 |
| `requestedLimit` | Number | 请求的结果数量限制 |
| `category` | String | 筛选的产品类别 |

### 空结果响应
```json
{
  "success": true,
  "code": 200,
  "message": "搜索成功",
  "data": {
    "productNames": [],
    "total": 0,
    "searchTime": 45,
    "meta": {
      "keyword": "xyz123",
      "requestedLimit": 10,
      "category": "all",
      "description": "输入关键词搜索匹配的产品名称"
    }
  },
  "timestamp": 1705123456789
}
```

### 错误响应

#### 参数错误 (HTTP 400)
```json
{
  "success": false,
  "code": 400,
  "message": "请求参数错误",
  "data": "搜索关键词不能为空",
  "timestamp": 1705123456789
}
```

#### 限流错误 (HTTP 429)  
```json
{
  "success": false,
  "code": 429,
  "message": "搜索请求过于频繁，请稍后重试",
  "timestamp": 1705123456789
}
```

## 使用场景

### 1. 🔍 产品对比自动完成
用户在产品对比页面输入时，实时显示匹配的产品名称：

```javascript
// 对比页面的产品输入框
function createCompareInput(index) {
  return `
    <div class="compare-input-wrapper">
      <input 
        type="text" 
        placeholder="输入产品名称" 
        data-index="${index}"
        class="product-input"
      />
      <div class="suggestions-dropdown" style="display: none;"></div>
    </div>
  `;
}

// 为每个输入框绑定自动完成
document.querySelectorAll('.product-input').forEach(input => {
  new ProductNameAutocomplete(input, input.nextElementSibling);
});
```

### 2. 📱 移动端友好交互
针对移动端优化的自动完成体验：

```css
/* 移动端优化样式 */
.suggestions-dropdown {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.suggestion-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.4;
}

.suggestion-item:hover {
  background-color: #f5f5f5;
}

.suggestion-item:last-child {
  border-bottom: none;
}
```

### 3. 🎯 智能搜索提示
提供搜索建议和用户引导：

```javascript
// 搜索建议功能
class SearchSuggestionHelper {
  static getSearchTips(keyword) {
    if (keyword.length < 2) {
      return "请输入至少2个字符开始搜索";
    }
    
    if (/^[a-zA-Z]+$/.test(keyword)) {
      return "提示：可以输入品牌名如 iPhone、MacBook、华为 等";
    }
    
    if (/[\u4e00-\u9fa5]/.test(keyword)) {
      return "提示：可以输入型号如 Pro、Max、Ultra 等";
    }
    
    return "输入产品名称关键词进行搜索";
  }
  
  static showTip(inputElement, keyword) {
    const tip = this.getSearchTips(keyword);
    // 显示提示信息
  }
}
```

## 最佳实践

### 🚀 **性能优化**
1. **防抖处理**：300ms延迟，避免频繁请求
2. **合理限制**：移动端建议5-8个结果
3. **本地缓存**：相同关键词缓存5分钟
4. **请求取消**：新请求发起时取消前一个

### 📱 **用户体验**
1. **加载状态**：显示搜索中的状态
2. **键盘导航**：支持上下键选择
3. **高亮显示**：匹配部分高亮显示
4. **空状态处理**：没有结果时的友好提示

### 🔧 **错误处理**
```javascript
async function robustSearchProductNames(keyword, retries = 2) {
  for (let i = 0; i <= retries; i++) {
    try {
      return await searchProductNames(keyword);
    } catch (error) {
      if (i === retries) {
        console.error('搜索失败，已重试', retries, '次');
        return [];
      }
      
      // 重试前等待
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}
```

### 🛡️ **安全考虑**
1. **输入验证**：前端验证关键词长度和格式
2. **XSS防护**：显示产品名称时HTML转义
3. **频率限制**：遵守API限流规则

## 集成步骤

### 1. 引入API调用函数
```javascript
// api.js
export async function searchProductNames(keyword, limit = 8, category = '') {
  // ... API调用实现
}
```

### 2. 创建自动完成组件
```javascript
// ProductAutocomplete.js
class ProductAutocomplete {
  // ... 组件实现
}
```

### 3. 在对比页面使用
```html
<!-- 产品对比页面 -->
<div class="product-compare">
  <div class="compare-input-group">
    <label>产品1</label>
    <input type="text" class="product-input" placeholder="输入产品名称" />
    <div class="suggestions"></div>
  </div>
  
  <div class="compare-input-group">
    <label>产品2</label>
    <input type="text" class="product-input" placeholder="输入产品名称" />
    <div class="suggestions"></div>
  </div>
</div>
```

## 测试建议

### 功能测试
- ✅ 不同长度关键词的搜索结果
- ✅ 中文、英文、数字的混合搜索
- ✅ 空结果和错误情况处理
- ✅ 类别筛选功能

### 性能测试
- ✅ 搜索响应时间（目标<100ms）
- ✅ 并发请求处理
- ✅ 限流机制验证

### 用户体验测试
- ✅ 移动端和PC端适配
- ✅ 键盘导航和交互
- ✅ 加载状态和错误提示

## 更新日志

### v2.0.0 (2024-01-15)
- 🎯 **重大更新**：简化为仅返回产品名称
- 🔍 优化搜索匹配策略，专注于产品名称匹配
- ⚡ 提升响应速度，优化自动完成体验
- 📝 简化API响应格式，减少数据传输
- 🧪 完善测试用例和文档 