# 对接京东电商API文档

## 需求分析
在"帮我选"微信小程序中，需要根据投票结果最高的产品名称，通过京东API直接跳转到该商品的购买页面，以替代现有的预设链接方式。

## 实现可行性
这个需求是可以实现的。京东提供了开放平台和相关API，开发者可以通过这些API进行商品搜索、获取商品详情和生成购买链接。

## 实现步骤

### 一、注册京东联盟（京东开放平台）

1. 访问京东联盟官网：https://union.jd.com/
2. 注册成为京东联盟会员（个人或企业均可）
3. 完成实名认证流程
4. 等待审核通过

### 二、创建应用并获取API权限

1. 登录京东联盟后台
2. 进入"我的工具" -> "API接口"
3. 创建应用，填写应用信息
4. 获取应用的AppKey和AppSecret（这是调用API的必要凭证）
5. 申请需要使用的API权限，主要包括：
   - 商品搜索API
   - 商品详情API
   - 推广链接生成API

### 三、开发对接流程

#### 1. 安装SDK（以Node.js为例）

```bash
npm install jd-union-sdk --save
```

#### 2. 初始化SDK配置

```javascript
const JDUnionAPI = require('jd-union-sdk');

const config = {
  appKey: '你的AppKey',
  appSecret: '你的AppSecret',
  unionId: '你的联盟ID', // 在京东联盟后台可以找到
  positionId: '你的推广位ID' // 在京东联盟后台创建
};

const jdClient = new JDUnionAPI(config);
```

#### 3. 根据产品名称搜索商品

```javascript
async function searchProduct(productName) {
  const params = {
    keyword: productName,
    pageIndex: 1,
    pageSize: 10
  };
  
  try {
    const result = await jdClient.goodsSearch(params);
    return result.data;
  } catch (error) {
    console.error('商品搜索失败:', error);
    return null;
  }
}
```

#### 4. 获取商品详情并生成购买链接

```javascript
async function generatePurchaseLink(skuId) {
  const params = {
    materialId: skuId, // 商品ID
    siteId: '', // 站点ID，可为空
    couponUrl: '', // 优惠券链接，可为空
    positionId: config.positionId // 推广位ID
  };
  
  try {
    const result = await jdClient.promotionCommon(params);
    return result.data.clickURL; // 返回生成的购买链接
  } catch (error) {
    console.error('生成购买链接失败:', error);
    return null;
  }
}
```

#### 5. 在小程序中集成API调用

修改小程序代码，将上述API调用集成到结果页面：

```javascript
// 在结果页面加载时
onLoad: function(options) {
  const topProduct = this.getTopVotedProduct(); // 获取投票最高的产品
  this.getProductLink(topProduct.name);
},

getProductLink: async function(productName) {
  // 调用云函数，避免将AppKey等敏感信息暴露在小程序端
  wx.cloud.callFunction({
    name: 'getJDProductLink',
    data: {
      productName: productName
    }
  }).then(res => {
    this.setData({
      productLink: res.result.link
    });
  }).catch(err => {
    console.error('获取商品链接失败', err);
  });
}
```

#### 6. 创建云函数处理API请求

创建名为`getJDProductLink`的云函数：

```javascript
// cloud functions: getJDProductLink
const cloud = require('wx-server-sdk');
const JDUnionAPI = require('jd-union-sdk');

cloud.init();

exports.main = async (event, context) => {
  const { productName } = event;
  
  const config = {
    appKey: '你的AppKey',
    appSecret: '你的AppSecret',
    unionId: '你的联盟ID',
    positionId: '你的推广位ID'
  };
  
  const jdClient = new JDUnionAPI(config);
  
  try {
    // 1. 搜索商品
    const searchParams = {
      keyword: productName,
      pageIndex: 1,
      pageSize: 5
    };
    
    const searchResult = await jdClient.goodsSearch(searchParams);
    if (!searchResult.data || searchResult.data.length === 0) {
      return { link: null, message: '未找到相关商品' };
    }
    
    // 2. 获取第一个商品结果（最相关）
    const firstProduct = searchResult.data[0];
    const skuId = firstProduct.skuId;
    
    // 3. 生成购买链接
    const linkParams = {
      materialId: skuId,
      positionId: config.positionId
    };
    
    const linkResult = await jdClient.promotionCommon(linkParams);
    return {
      link: linkResult.data.clickURL,
      productInfo: {
        name: firstProduct.skuName,
        price: firstProduct.priceInfo.price,
        image: firstProduct.imageInfo.imageUrl
      }
    };
    
  } catch (error) {
    return { error: error.message };
  }
};
```

### 四、链接转换为小程序内可用格式

京东生成的链接通常是网页链接，需要转换为小程序内可用的格式：

1. 使用京东提供的小程序跳转API
2. 或使用Web-view组件打开链接（需要在小程序后台配置业务域名）

```javascript
// 方法1：使用京东小程序跳转API
wx.navigateToMiniProgram({
  appId: 'wx91d27dbf599dff74', // 京东小程序的appId
  path: 'pages/item/detail?sku=' + skuId,
  success(res) {
    // 跳转成功
  },
  fail(err) {
    console.error(err);
  }
});

// 方法2：使用Web-view（需要在app.json中添加web-view组件）
// 在wxml中
<web-view src="{{productLink}}"></web-view>
```

### 五、注意事项和限制

1. **费用相关**：京东联盟可能会收取一定的佣金分成，具体费率根据商品类目不同而异
2. **API调用限制**：有每日调用次数限制，需关注配额使用情况
3. **审核要求**：小程序跳转到电商需符合微信小程序的规范要求
4. **数据安全**：AppKey等敏感信息应保存在服务端，不要暴露在小程序代码中
5. **商品搜索精确度**：根据产品名称搜索可能不够精确，建议增加更多筛选条件，如品牌、型号等

### 六、优化建议

1. **缓存机制**：对于热门产品，可以缓存API结果，减少API调用次数
2. **搜索策略**：实现模糊搜索和精确搜索结合的策略，提高匹配精度
3. **多电商平台支持**：考虑同时支持淘宝、拼多多等其他电商平台
4. **用户反馈**：增加反馈机制，当商品不匹配时允许用户反馈

## 参考资源

- 京东联盟官网：https://union.jd.com/
- 京东联盟API文档：https://union.jd.com/openplatform/api
- 微信小程序开发文档：https://developers.weixin.qq.com/miniprogram/dev/framework/
