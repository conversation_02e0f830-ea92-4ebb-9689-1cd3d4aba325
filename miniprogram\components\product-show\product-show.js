// components/product-show/product-show.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 产品列表
    products: {
      type: Array,
      value: []
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 加载更多状态
    loadingMore: {
      type: Boolean,
      value: false
    },
    // 是否已经搜索过
    hasSearched: {
      type: Boolean,
      value: false
    },
    // 总产品数量
    totalCount: {
      type: Number,
      value: 0
    },
    // 是否有更多数据
    hasMore: {
      type: Boolean,
      value: false
    },
    // 滚动位置
    scrollTop: {
      type: Number,
      value: 0
    },
    // 详情页路径（可选）- 提供更灵活的导航配置
    detailPagePath: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 组件内部状态
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 产品点击事件
     * 遵循组件最佳实践，组件不应直接处理导航逻辑
     * 而是通过事件通知父组件，由父组件决定如何处理
     */
    onProductTap(e) {
      const product = e.currentTarget.dataset.product;
      console.log('点击产品:', product);
      
      // 触发自定义事件，将点击的产品数据传递给父组件
      this.triggerEvent('productTap', { product });
    },

    /**
     * 图片加载错误处理
     */
    onImageError(e) {
      console.log('图片加载失败:', e);
      // 触发自定义事件，通知父组件图片加载失败
      this.triggerEvent('imageError', e);
    },

    /**
     * 手动加载更多产品 - 用户点击文字链接触发
     */
    onLoadMoreClick() {
      if (!this.properties.hasMore || this.properties.loadingMore || this.properties.loading) {
        console.log('无法加载更多:', {
          hasMore: this.properties.hasMore,
          loadingMore: this.properties.loadingMore,
          loading: this.properties.loading
        });
        return;
      }
      
      console.log('用户点击查看更多文字链接');
      
      // 触发自定义事件，通知父组件加载更多
      this.triggerEvent('loadMore');
    },

    /**
     * scroll-view 滚动到底部事件
     */
    onScrollToLower() {
      // 触发自定义事件，通知父组件滚动到底部
      this.triggerEvent('scrollToLower');
    },

    /**
     * 对比按钮点击事件
     * 阻止事件冒泡，避免触发产品点击事件
     */
    onCompareToggle(e) {
      const product = e.currentTarget.dataset.product;
      console.log('点击对比按钮:', product);
      
      // 触发自定义事件，通知父组件处理对比逻辑
      this.triggerEvent('compareToggle', { product });
    }
  }
});