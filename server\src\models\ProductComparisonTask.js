const mongoose = require('mongoose');

/**
 * 产品对比任务模型
 * 用于管理异步产品对比任务的状态和结果
 */
const ProductComparisonTaskSchema = new mongoose.Schema(
  {
    // 任务基本信息
    taskId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    
    // 用户信息
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    
    // 对比的产品名称列表
    productNames: {
      type: [String],
      required: true,
      validate: {
        validator: function(names) {
          return names && names.length >= 2 && names.length <= 6;
        },
        message: '产品名称列表必须包含2-6个产品'
      }
    },
    
    // 任务状态
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending',
      index: true
    },
    
    // 任务进度 (0-100)
    progress: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    
    // 任务开始时间
    startedAt: {
      type: Date,
      default: null
    },
    
    // 任务完成时间
    completedAt: {
      type: Date,
      default: null
    },
    
    // 对比结果 (任务完成后存储)
    result: {
      type: mongoose.Schema.Types.Mixed,
      default: null
    },
    
    // 错误信息 (任务失败时存储)
    error: {
      message: {
        type: String,
        default: null
      },
      stack: {
        type: String,
        default: null
      },
      timestamp: {
        type: Date,
        default: null
      }
    },
    
    // 任务元数据
    metadata: {
      // 处理模式
      processingMode: {
        type: String,
        default: 'parallel'
      },
      // AI模型
      aiModel: {
        type: String,
        default: 'deepseek-chat'
      },
      // 处理耗时 (毫秒)
      processingTime: {
        type: Number,
        default: null
      },
      // 缓存ID (如果使用了缓存)
      comparisonCacheId: {
        type: mongoose.Schema.Types.ObjectId,
        default: null
      }
    },
    
    // 通知状态
    notificationSent: {
      type: Boolean,
      default: false
    },
    
    // 任务过期时间 (24小时后自动清理)
    expiresAt: {
      type: Date,
      default: () => new Date(Date.now() + 24 * 60 * 60 * 1000),
      index: { expireAfterSeconds: 0 }
    }
  },
  {
    timestamps: true
  }
);

// 索引优化
ProductComparisonTaskSchema.index({ userId: 1, status: 1 });
ProductComparisonTaskSchema.index({ status: 1, createdAt: -1 });
ProductComparisonTaskSchema.index({ taskId: 1, userId: 1 });

// 静态方法：生成唯一任务ID
ProductComparisonTaskSchema.statics.generateTaskId = function() {
  return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 静态方法：创建新任务
ProductComparisonTaskSchema.statics.createTask = async function(userId, productNames) {
  const taskId = this.generateTaskId();
  
  return await this.create({
    taskId,
    userId,
    productNames,
    status: 'pending'
  });
};

// 实例方法：更新任务状态
ProductComparisonTaskSchema.methods.updateStatus = async function(status, additionalData = {}) {
  this.status = status;
  
  if (status === 'processing') {
    this.startedAt = new Date();
  } else if (status === 'completed' || status === 'failed') {
    this.completedAt = new Date();
    
    if (this.startedAt) {
      this.metadata.processingTime = this.completedAt - this.startedAt;
    }
  }
  
  // 合并额外数据
  Object.assign(this, additionalData);
  
  return await this.save();
};

// 实例方法：设置任务结果
ProductComparisonTaskSchema.methods.setResult = async function(result) {
  return await this.updateStatus('completed', { 
    result,
    progress: 100
  });
};

// 实例方法：设置任务错误
ProductComparisonTaskSchema.methods.setError = async function(error) {
  return await this.updateStatus('failed', {
    error: {
      message: error.message,
      stack: error.stack,
      timestamp: new Date()
    },
    progress: 0
  });
};

// 实例方法：更新进度
ProductComparisonTaskSchema.methods.updateProgress = async function(progress) {
  this.progress = Math.max(0, Math.min(100, progress));
  return await this.save();
};

module.exports = mongoose.model('ProductComparisonTask', ProductComparisonTaskSchema);
